/**
 * 通用Loading初始化脚本
 * 用于在各个页面中快速初始化loading效果
 */

// 页面配置映射
const PAGE_CONFIGS = {
    'schedule_management.html': {
        title: '主播排班',
        customResources: [
            { check: () => typeof XLSX !== 'undefined', name: 'XLSX' }
        ]
    },
    'distribution_rules.html': {
        title: '销售排班',
        customResources: [
            { check: () => typeof axios !== 'undefined', name: 'Axios' }
        ]
    },
    'distribution_plan.html': {
        title: '分发计划',
        customResources: []
    },
    'home.html': {
        title: '首页',
        customResources: []
    },
    'login.html': {
        title: '登录系统',
        customResources: []
    },
    'user_management.html': {
        title: '用户管理',
        customResources: []
    },
    'user_review.html': {
        title: '用户审核',
        customResources: []
    }
};

/**
 * 初始化Loading效果
 * @param {string} pageKey - 页面标识符（可选）
 * @param {Object} customConfig - 自定义配置（可选）
 */
function initPageLoading(pageKey = null, customConfig = {}) {
    // 自动检测页面
    if (!pageKey) {
        const currentPath = window.location.pathname;
        const fileName = currentPath.split('/').pop() || 'index.html';
        
        // 尝试从URL路径匹配页面配置
        for (const [key, config] of Object.entries(PAGE_CONFIGS)) {
            if (currentPath.includes(key.replace('.html', '')) || fileName === key) {
                pageKey = key;
                break;
            }
        }
    }
    
    // 获取页面配置
    const config = PAGE_CONFIGS[pageKey] || {
        title: '系统',
        customResources: []
    };
    
    // 合并自定义配置
    const finalConfig = {
        ...config,
        ...customConfig
    };
    
    console.log(`🔧 初始化${finalConfig.title}Loading效果...`);
    
    // 页面DOM加载完成后开始检测
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            startLoadingProcess(finalConfig);
        });
    } else {
        // DOM已经加载完成
        startLoadingProcess(finalConfig);
    }
}

/**
 * 开始Loading流程
 * @param {Object} config - 页面配置
 */
function startLoadingProcess(config) {
    // 确保LoadingManager已加载
    if (typeof LoadingManager === 'undefined') {
        console.error('❌ LoadingManager未找到，请确保loading组件已正确引入');
        return;
    }
    
    const loadingManager = new LoadingManager(config.title);
    
    // 如果有自定义的加载完成回调
    if (config.onComplete && typeof config.onComplete === 'function') {
        loadingManager.onLoadingComplete = config.onComplete;
    }
    
    // 开始加载检测
    loadingManager.startLoading(config.customResources);
}

/**
 * 快速初始化函数（简化版）
 * @param {string} title - 页面标题
 * @param {Array} customResources - 自定义资源检测
 * @param {Function} onComplete - 完成回调
 */
function quickInitLoading(title = '系统', customResources = [], onComplete = null) {
    initPageLoading(null, {
        title: title,
        customResources: customResources,
        onComplete: onComplete
    });
}

// 暴露到全局
window.initPageLoading = initPageLoading;
window.quickInitLoading = quickInitLoading;
