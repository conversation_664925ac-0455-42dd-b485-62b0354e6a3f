# 线索分发系统后端

基于 FastAPI 构建的企业级线索分发管理系统，专为电商和新媒体渠道的线索管理与分发而设计。

## 🌟 项目概述

本系统是一个综合性的线索管理平台，提供完整的线索收集、分发、跟进和管理功能。支持多渠道线索接入、智能分发算法、权限管理、数据统计等核心功能。

## 🔧 技术栈

- **后端框架**: FastAPI 0.100.0
- **数据库**: MySQL (使用 SQLAlchemy ORM)
- **身份验证**: JWT + bcrypt
- **前端模板**: Jinja2
- **数据加密**: Cryptography + Fernet
- **部署**: Gunicorn/uWSGI
- **数据库迁移**: Alembic

## 📁 项目结构

```
Lead-Distribution_backend/
├── alembic/                    # Alembic 数据库迁移文件
│   └── versions/              # 数据库版本文件
├── config/                    # 配置文件目录
├── migrations/                # 额外的数据库迁移文件
│   └── versions/
├── routers/                   # API 路由模块
│   ├── leads.py              # 线索管理相关API
│   ├── leads_withdraw.py     # 线索提取三表联查API
│   ├── user_review.py        # 用户审核API
│   ├── user_management.py    # 用户管理API
│   ├── presets.py            # 表单预设API
│   ├── recycle_bin.py        # 回收站API
│   ├── schedule.py           # 排班管理API
│   ├── distribution.py       # 分发规则API
│   ├── monitoring.py         # 监控API
│   ├── frontend_api.py       # 前端专用API
│   ├── email_seed.py         # 邮件发送API
│   ├── category.py           # 分类管理API
│   ├── rules_openseas.py     # 公海转换规则配置API
│   └── wechat_applet.py      # 微信小程序API
├── static/                    # 静态文件目录
│   ├── css/                  # 样式文件
│   ├── js/                   # JavaScript 文件
│   │   └── components/       # JS 组件
│   └── uploads/              # 上传文件存储
│       └── withdraw_evidence/ # 撤回凭证存储
├── templates/                 # HTML 模板文件
│   ├── components/           # 模板组件
│   ├── login.html           # 登录页面
│   ├── home.html            # 首页
│   ├── user_review.html     # 用户审核页面
│   ├── user_management.html # 用户管理页面
│   ├── form_preset.html     # 表单预设页面
│   ├── schedule_management.html # 排班管理页面
│   ├── distribution_rules.html  # 分发规则页面
│   ├── distribution_plan.html   # 分发计划页面
│   ├── status.html          # 状态页面
│   └── 404.html             # 404错误页面
├── utils/                     # 工具模块
│   ├── encryption.py         # 数据加密工具
│   ├── queue_monitor.py      # 队列监控工具
│   └── pending_data.py       # 待处理数据工具
├── venv/                      # Python 虚拟环境
├── main.py                    # FastAPI 应用主入口
├── models.py                  # SQLAlchemy 数据模型定义
├── database.py                # 数据库连接配置
├── auth.py                    # 身份验证中间件
├── requirements.txt           # Python 依赖包
├── alembic.ini               # Alembic 配置文件
├── gunicorn_conf.py          # Gunicorn 配置文件
├── uwsgi.ini                 # uWSGI 配置文件
└── 其他数据库脚本和工具文件
```

## 📋 核心功能模块

### 1. 用户管理系统
- **用户注册与审核**: 支持多部门用户注册，管理员审核机制
- **权限管理**: 基于部门和身份的分级权限控制
- **身份验证**: JWT Token + 密码哈希安全验证

### 2. 线索管理系统
- **线索录入**: 支持多渠道线索信息录入
- **数据加密**: 敏感信息（手机号、微信号等）自动加密存储
- **状态管理**: 线索状态跟踪（已提交、等待中、已发送等）
- **撤回申请**: 支持线索撤回审批流程
- **三表联查**: 整合线索基础信息、CRM数据和提取记录的综合查询

### 3. 分发系统
- **智能分发**: 基于规则的自动线索分发
- **队列管理**: 免费队列和付费队列分离
- **分组分发**: 支持按分组和时间段进行分发
- **时间分组排序**: 基于time_slot字段的时间分组排序算法，确保早时间优先、组内轮询分配
- **实时监控**: 分发状态实时监控
- **批量操作**: 支持批量修改和批量删除分发规则
  - **批量修改**: 一次性修改多条规则的班次、预计数量、店铺等字段
  - **批量删除**: 支持多选删除，使用软删除保障数据安全

### 4. 排班管理
- **主播排班**: 支持主播的班次安排
- **店铺管理**: 多店铺、多渠道排班支持
- **时间管理**: 灵活的时间段设置

### 5. 数据统计与监控
- **实时监控**: 系统状态和队列监控
- **数据统计**: 线索数量、分发效果统计
- **性能监控**: 系统性能指标监控

### 6. 表单预设系统
- **模板管理**: 支持自定义表单模板
- **数据格式化**: 智能数据格式化功能
- **批量处理**: 支持批量数据处理

### 7. 分类管理系统
- **多级分类**: 支持最多三级分类嵌套结构
- **树形展示**: 提供树形结构和扁平化两种数据展示方式
- **软删除**: 软删除机制，保障数据安全
- **模糊搜索**: 支持按名称、关键词、父级分类进行模糊搜索
- **权重排序**: 支持分类权重排序，灵活控制显示顺序

### 8. 自动化任务系统
- **定时任务调度**: 基于BackgroundTasks + time.sleep的自动化任务调度
- **动态时间调整**: 支持实时修改任务启动时间并重新调度
- **开关控制**: 可动态开启或关闭定时任务
- **队列自动生成**: 按设定时间自动生成分发队列
- **任务状态管理**: 实时监控任务运行状态和线程状态
- **任务恢复机制**: 应用重启后自动恢复活跃任务

### 9. WebSocket线索推送系统 ✅ 已实现 (2025-06-27 更新)
- **连接池管理**: 使用字典结构维护用户ID到WebSocket连接的映射
- **实时线索推送**: 当有新线索分配时，通过WebSocket实时推送通知给在线用户
- **事务性消息**: 确保数据库更新和消息推送的一致性
- **连接状态监控**: 实时监控WebSocket连接状态，自动处理断线重连
- **用户在线状态**: 只向在线用户推送消息，离线用户跳过推送
- **消息格式化**: 支持JSON格式的结构化消息推送
- **错误处理**: 完善的异常处理机制，确保系统稳定性
- **多端点支持**: 支持路径参数和查询参数两种连接方式
- **查询优化**: 修复数据库JOIN查询问题，增加备用查询机制

#### 核心组件
- **WebSocketManager类**: 管理所有WebSocket连接的生命周期
- **WebSocket端点**:
  - `/api/websocket/{user_id}` - 路径参数方式连接
  - `/api/websocket?token={user_id}` - 查询参数方式连接（兼容前端）
  - `/api/test-websocket` - 简单测试端点
- **推送接口**: `/api/xiansuo_websocket` 执行线索推送逻辑
- **状态监控**: `/api/websocket/status` 查看连接状态
- **调试接口**: `/api/websocket/debug` 调试数据库查询问题
- **登录集成**: `/api/login_websocket` 获取连接信息

#### 推送流程
1. 查询当日未推送的已分发线索（`email_seeded=0`, `status="distributed"`）
2. 按用户分组统计线索数量（支持JOIN查询和子查询备用机制）
3. 检查用户在线状态，只向在线用户推送
4. 发送JSON格式的WebSocket消息
5. 推送成功后更新`email_seeded=1`标记为已推送

#### 最新修复 (2025-06-27)
- ✅ 修复WebSocket库缺失问题，安装websockets支持
- ✅ 修复数据库查询逻辑，增加异常处理和备用查询
- ✅ 优化连接端点，支持多种URL格式
- ✅ 增强日志记录，便于问题排查
- ✅ 添加调试接口，实时监控查询状态
- ✅ 升级模板消息格式，集成排班数据统计

#### 模板消息升级 (2025-06-27)
**新模板格式**：`你收到了N条线索，今日预计接收M条，当前已接收X条，剩余Y条，请前往查看`

**数据来源**：
- N：当前推送的线索数量
- M：DistributionRule表中用户当日所有排班的预计接收总数（expected_total + expected_free + expected_paid）
- X：DistributionRule表中用户当日所有排班的实际接收总数（actual_total + actual_free + actual_paid）
- Y：剩余数量（M - X）

**查询条件**：`DistributionRule.date = current_date AND DistributionRule.member = user_name`

## 🗄️ 数据库模型

### 核心数据表

1. **clue_sheet_2025** - 线索基础信息表
   - 存储线索的完整信息
   - 支持敏感数据加密
   - 包含撤回申请、开单状态等扩展字段

2. **sala_crm** - 销售CRM数据表
   - 存储线索的销售跟进信息
   - 包含分配日期、跟进状态、失败原因等
   - 支持销售人员的业务数据管理

3. **clue_withdraw** - 线索提取记录表
   - 记录线索的提取和处理历史
   - 支持提取状态跟踪
   - 与线索表和CRM表形成完整的数据链条

4. **users** - 用户表
   - 用户基本信息和认证数据
   - 部门和权限信息
   - 支持微信OpenID绑定

## 🔧 修复日志

### 2025-01-08 - 修复客情记录更新接口调试代码问题
- **问题**: `/api/sala_crm/{lead_id}` PUT接口存在调试代码导致无法正常更新客情记录
- **修复内容**:
  - 删除 `update_sala_crm` 函数开头的 `return data` 调试语句
  - 删除处理 `customer_record` 字段时的 `return data.customer_record` 调试语句
  - 修复 `add_history_data` 函数中的 `return new_record` 调试语句
- **影响**: 现在客情记录更新功能正常工作，历史记录追加功能正常
- **涉及文件**: `routers/sala_crm.py`

5. **user_permissions_backend/frontend** - 权限表
   - 后台和前台权限分离管理
   - 基于部门和身份的权限控制

6. **distribution_rules** - 分发规则表
   - 分发规则配置
   - 支持按日期、渠道类型设置

7. **schedules** - 排班表
   - 主播排班信息
   - 支持多店铺、多渠道

8. **channels/stores/anchors** - 基础数据表
   - 渠道、店铺、主播基础信息管理

9. **category** - 分类管理表
   - 支持三级分类嵌套结构
   - 软删除机制，状态管理
   - 权重排序，关键词搜索

10. **distribution_rules_task** - 自动化任务表
    - 任务启动时间和状态控制
    - 支持队列生成的定时调度
    - 每日唯一任务记录

## 🚀 安装与部署

### 环境要求
- Python 3.8+
- MySQL 5.7+
- Redis (可选，用于缓存)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd Lead-Distribution_backend
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **环境配置**
创建 `.env` 文件并配置以下变量：
```env
DB_HOST=localhost
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=lead_distribution
JIEKO_SECRET_KEY=your_secret_key
JIEKO_API_KEY=your_api_key
```

5. **数据库初始化**
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE lead_distribution CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 运行数据库迁移
alembic upgrade head

# 或使用提供的脚本
python create_tables.py
```

6. **启动应用**
```bash
# 开发环境
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# 生产环境
gunicorn -c gunicorn_conf.py main:app
```

## 🔗 API 接口文档

### 公海转换规则配置接口

#### GET /api/rules_openseas/list
**功能描述**：获取公海转换规则列表，支持分页和多条件筛选

**请求参数**：
- `page` (int, 可选): 页码，默认为1
- `page_size` (int, 可选): 每页数量，默认为10，最大100
- `rule_type` (int, 可选): 规则类型筛选（0：默认条件，1：特定条件，2：排除条件）
- `channel` (str, 可选): 渠道筛选
- `queue` (int, 可选): 线索类型筛选（0：付费店铺，1：免费店铺）

**响应示例**：
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "weigh": 10,
            "rule_type": 1,
            "channel": "新媒体渠道",
            "queue": 0,
            "fname": "线索来源",
            "fvalue": "抖音",
            "tip": "等于",
            "hours": 24,
            "switch_stages": 0,
            "cf_contact_person": 0,
            "remarks": "测试规则"
        }
    ],
    "total": 1,
    "page": 1,
    "page_size": 10,
    "total_pages": 1
}
```

#### GET /api/rules_openseas/{rule_id}
**功能描述**：根据ID获取单个公海转换规则详情

#### POST /api/rules_openseas/create
**功能描述**：创建新的公海转换规则（简化版）

**请求体示例**：
```json
{
    "weigh": 1
}
```

**完整字段示例**：
```json
{
    "weigh": 10,
    "rule_type": 1,
    "channel": "新媒体渠道",
    "queue": 0,
    "fname": "线索来源",
    "fvalue": "抖音",
    "tip": "等于",
    "hours": 24,
    "switch_stages": 0,
    "cf_contact_person": 0,
    "remarks": "测试规则"
}
```

**响应示例**：
```json
{
    "success": true,
    "id": 123
}
```

**字段说明**：
- 只需要提供必要字段，其他字段会使用默认值
- `weigh`: 优先级（必填）
- 其他字段为可选，未提供时使用默认值

#### PUT /api/rules_openseas/{rule_id}
**功能描述**：更新指定ID的公海转换规则（单个更新）

#### PUT /api/rules_openseas/batch_update
**功能描述**：批量更新公海转换规则（优化版）

**请求体示例**：
```json
[{
    "id": 51,
    "rule_type": 0,
    "channel": "",
    "queue": null,
    "fname": "",
    "tip": "",
    "fvalue": "",
    "hours": "0",
    "switch_stages": null,
    "cf_contact_person": 0,
    "remarks": "",
    "weigh": 4
}]
```

**字段说明**：
- `id`: 必需，要更新的规则ID
- `rule_type`: 规则类型（0：默认条件，1：特定条件，2：排除条件）
- `channel`: 渠道（空字符串会被设置为null）
- `queue`: 线索类型（null、0付费店铺、1免费店铺）
- `fname`: 字段名称（空字符串会被设置为null）
- `tip`: 条件符号（空字符串会被设置为null）
- `fvalue`: 内容（空字符串会被设置为null）
- `hours`: 时间(H)，支持字符串类型，会自动转换为整数
- `switch_stages`: 转换策略（null、0组内转换、1跨组转换）
- `cf_contact_person`: 对接人重复（0不能是自己、1可以是自己）
- `remarks`: 备注（空字符串会被设置为null）
- `weigh`: 优先级

**响应示例**：
```json
{
    "success": true,
    "message": "成功更新 1 条规则，失败 0 条",
    "updated_count": 1,
    "failed_count": 0,
    "updated_rules": [
        {
            "index": 0,
            "id": 51,
            "updated_fields": {
                "rule_type": 0,
                "channel": null,
                "queue": null,
                "weigh": 4
            },
            "data": {...}
        }
    ],
    "failed_rules": []
}
```

**特性**：
- 支持空值处理：空字符串自动转换为null
- 类型转换：字符串类型的hours自动转换为整数
- 详细反馈：返回每个规则的更新字段和结果
- 错误处理：单个规则失败不影响其他规则

#### DELETE /api/rules_openseas/{rule_id}
**功能描述**：删除指定ID的公海转换规则（软删除）

#### DELETE /api/rules_openseas/batch_delete
**功能描述**：批量删除公海转换规则（软删除）

#### PUT /api/rules_openseas/{rule_id}/weigh
**功能描述**：更新规则的优先级

#### GET /api/rules_openseas/statistics
**功能描述**：获取公海转换规则的统计信息

#### GET /api/rules_openseas/zhuanhua
**功能描述**：执行公海转换操作，根据配置的规则将符合条件的数据从SalaCrm转移到SalaCrmOpenseas

**处理流程**：
1. **查询规则**：获取所有有效的转换规则（is_delete=0），按优先级排序
2. **数据匹配**：根据规则类型进行不同的匹配策略
   - **特定条件（rule_type=1）**：在SalaCrmToOpenseas表中匹配数据
   - **默认条件（rule_type=0）**：在SalaCrm+ClueSheet表中匹配数据（os_status=0）
   - **排除条件（rule_type=2）**：在SalaCrm+ClueSheet表中匹配数据（os_status=0），但排除匹配的记录
3. **条件检查**：
   - 渠道匹配（channel）
   - 线索类型匹配（queue：-1不限制，0付费，1免费）
   - 字段值匹配（fname + tip + fvalue）
   - 时间条件（hours）
4. **数据转移**：将匹配的数据复制到SalaCrmOpenseas表
5. **状态更新**：
   - 更新SalaCrm.os_status=1
   - 更新SalaCrmToOpenseas.is_trans=1（特定条件）

**匹配条件说明**：
- **渠道**：新媒体/电商
- **线索类型**：-1不限制，0付费店铺，1免费店铺
- **条件符号**：支持 =、!=、>、<、>=、<=、包含、不包含
- **时间计算**：
  - 特定条件：以SalaCrmToOpenseas.ftime为开始时间
  - 默认/排除条件：以SalaCrm.allocation_date为开始时间

**响应示例**：
```json
{
    "success": true,
    "message": "公海转换完成，共处理 15 条数据",
    "total_processed": 15,
    "rule_results": [
        {
            "rule_id": 1,
            "rule_type": 1,
            "matched_count": 8,
            "success_count": 8
        },
        {
            "rule_id": 2,
            "rule_type": 0,
            "matched_count": 7,
            "success_count": 7
        }
    ]
}
```

**注意事项**：
- 避免重复处理：已处理的ID不会重复转换
- 事务安全：所有操作在同一事务中完成
- 错误处理：单个规则失败不影响其他规则执行

### 公海转换自动触发机制

**功能描述**：在 `PUT /api/sala_crm` 接口中集成的自动公海转换功能，根据配置的规则自动判断线索是否需要转入公海。

**触发条件**：
- 当更新 SalaCrm 记录时自动执行
- 匹配 `RulesOpenseas` 表中 `rule_type == 1`（特定条件）的规则
- 同时满足以下条件：
  - `channel` 字段与线索渠道一致
  - `queue` 字段与线索队列类型一致
  - `fname` 字段等于提交数据中某个非空字段的字段名
  - `fvalue` 字段等于提交数据中对应字段的值
  - 根据 `tip` 条件符号进行匹配（等于、不等于、包含、不包含）

**处理逻辑**：
- 匹配成功后自动创建 `SalaCrmToOpenseas` 记录
- 记录触发时间、字段名称、转换策略等信息
- 确保同一 `lead_id` 和 `fname` 组合不重复记录
- 支持更新已存在记录的触发时间

**使用示例**：
```json
PUT /api/sala_crm/20250707000054
{
    "clue_stage": "无效"
}
```
如果存在规则：`fname="clue_stage"`, `fvalue="无效"`, `tip="等于"`，则自动触发公海转换。

### 线索管理三表联查接口

#### POST /api/leads_withdraw

**功能描述**：以ClueWithdraw为主表，整合线索基础信息、CRM数据和提取记录的综合查询接口

**请求参数**：
```json
{
    "page": 1,                    // 页码（可选，默认1）
    "pageSize": 20,               // 页面大小（可选，默认20）
    "keyword": "搜索关键字",       // 关键字搜索（可选）
    "sortField": "apply_time",    // 排序字段（可选）
    "sortOrder": "desc"           // 排序顺序（可选：asc/desc）
}
```

#### POST /api/leads_sala_crm2

**功能描述**：以ClueSheet为主表的三表联合查询接口，提供完整的线索管理数据视图

**请求参数**：
```json
{
    "page": 1,                    // 页码（可选，默认1）
    "pageSize": 20,               // 页面大小（可选，默认20）
    "allocation_date": "2025-01-15", // 分配日期过滤（可选，支持 YYYY-MM-DD 或 YYYY/MM/DD 格式）
    "keyword": "搜索关键字",       // 关键字搜索（可选）
    "filters": [                  // 高级过滤条件（可选）
        {
            "clue_basic_tag": ["A级", "B级"]  // 多选标签过滤
        },
        {
            "clue_stage": ["意向", "已签约"]  // 多选阶段过滤
        },
        {
            "allocation_date": {      // 日期范围过滤
                "start": "2025-01-01",
                "end": "2025-01-31"
            }
        },
        {
            "status": 1               // 单值过滤
        }
    ],
    "sortField": "record_time",   // 排序字段（可选，支持跨表字段）
    "sortOrder": "desc"           // 排序顺序（可选：asc/desc）
}
```

**响应格式**：
```json
{
    "success": true,
    "message": "查询成功",
    "data": {
        "list": [
            {
                "clue_sheet": {
                    "ID": "202501150001",
                    "record_date": "2025-01-15",
                    "wechat_id": "微信号（已解密）",
                    "phone_number": "手机号码（已解密）",
                    "channel": "抖音",
                    "store": "旗舰店",
                    "contact_person": "张三",
                    "registrar": "李四",
                    "anchor": "主播A",
                    "status": 1,
                    "remarks": "备注信息",
                    "record_time": "2025-01-15 14:30:00"
                },
                "sala_crm": {           // 可能为null
                    "allocation_date": "2025-01-15",
                    "wechat_name": "客户微信昵称",
                    "is_add": 1,
                    "customer_name": "王五",
                    "is_billed": 0,
                    "bill_number": null,
                    "clue_basic_tag": "A级",
                    "clue_stage": "意向",
                    "last_followup_time": "2025-01-15 16:20:00",
                    "last_followup_record": "已添加微信",
                    "failure_analysis": null
                },
                "clue_withdraw": {      // 可能为null
                    "contact_person": "张三",
                    "withdraw_reason": "客户要求",
                    "withdraw_evidence": "screenshot.jpg",
                    "apply_time": "2025-01-15 18:00:00",
                    "reviewer": "审核员",
                    "shenpi_time": "2025-01-15 19:00:00",
                    "withdraw_status": 1,
                    "receipt": "审核通过"
                }
            }
        ],
        "total": 100,        // 总记录数
        "page": 1,           // 当前页码
        "pageSize": 20,      // 页面大小
        "totalPages": 5      // 总页数
    }
}
```

**功能特点**：
- **以ClueSheet为主表**：确保所有线索记录都能被查询到，关联表数据缺失不影响主表数据展示
- **智能权限控制**：根据用户部门和权限级别自动限制数据访问范围
  - 销售部：支持个人/组内/全部三级权限控制
  - 新媒体部：只能查看自己登记的数据
  - 超管：无限制访问
- **多表跨字段搜索**：支持在三个表的多个字段中进行关键词模糊搜索
- **灵活的过滤条件**：支持单值过滤、多选过滤、日期范围过滤等多种过滤方式
- **智能排序**：自动识别排序字段所属表，支持跨表排序
- **数据安全**：自动解密敏感数据（手机号、微信号）
- **完整的错误处理**：包含参数验证、权限验证、数据库异常处理
- **结构化响应**：按表分组的清晰数据结构，便于前端处理

### 分类管理接口

#### POST /api/category - 添加分类

**功能描述**：创建新的分类项，支持指定父级分类

**请求参数**：
```json
{
    "type": "商品",                // 分类类型（可选）
    "name": "手机",               // 分类名称（必填）
    "keywords": "苹果 小米 华为",   // 关键词（可选）
    "pid": 1                      // 父级ID，0表示一级分类（可选，默认0）
}
```

**响应格式**：
```json
{
    "code": 200,
    "message": "分类添加成功",
    "data": {
        "id": 1,
        "pid": 0,
        "type": "商品",
        "name": "手机",
        "keywords": "苹果 小米 华为",
        "weight": 1,
        "status": 1,
        "parent_name": null
    }
}
```

#### PUT /api/category/{id} - 编辑分类

**功能描述**：修改已有分类的信息

**请求参数**：
```json
{
    "type": "数码产品",            // 分类类型（可选）
    "name": "智能手机",            // 分类名称（可选）
    "keywords": "5G 高清 拍照",    // 关键词（可选）
    "pid": 2                      // 父级ID（可选）
}
```

#### DELETE /api/category/{id} - 删除分类

**功能描述**：软删除分类项，仅修改status字段为0

**响应格式**：
```json
{
    "code": 200,
    "message": "分类删除成功",
    "data": {
        "affected_rows": 1
    }
}
```

#### POST /api/categorydata - 查询分类

**功能描述**：多条件查询分类数据，支持模糊搜索和分页

**请求参数**：
```json
{
    "name": "手机",               // 分类名称模糊搜索（可选）
    "keywords": "5G",            // 关键词模糊搜索（可选）
    "parent_name": "电子产品",   // 一级分类名称筛选（可选）
    "page": 1,                   // 页码（可选，默认1）
    "page_size": 10              // 每页数量（可选，默认10）
}
```

**响应格式**：
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "categories": [          // 分页的扁平化列表
            {
                "id": 1,
                "pid": 0,
                "type": "商品",
                "name": "电子产品",
                "keywords": "数码 电子",
                "weight": 1,
                "status": 1,
                "parent_name": null
            }
        ],
        "tree": [                // 完整的树形结构
            {
                "id": 1,
                "name": "电子产品",
                "children": [
                    {
                        "id": 2,
                        "name": "手机",
                        "children": []
                    }
                ]
            }
        ],
        "total": 10,             // 总记录数
        "page": 1,               // 当前页码
        "page_size": 10          // 页面大小
    }
}
```

#### GET /api/category/parents - 获取一级分类

**功能描述**：获取所有一级分类列表，用于下拉选择框

**响应格式**：
```json
{
    "code": 200,
    "message": "获取一级分类成功",
    "data": [
        {
            "id": 1,
            "name": "电子产品",
            "type": "商品"
        }
    ]
}
```

**分类管理功能特点**：
- 支持三级分类嵌套结构，最多三层父子关系
- 软删除机制，通过status字段控制分类状态
- 支持按名称、关键词、父级分类进行模糊搜索
- 提供树形结构和扁平化两种数据展示方式
- 自动权重设置，支持分类排序
- 完善的数据验证和错误处理机制
- 防止循环引用，确保分类结构的合理性

## 👥 用户管理与权限系统详解

### 用户角色体系

系统采用四级用户角色管理，通过 `is_admin` 字段区分：

| 角色值 | 角色名称 | 权限级别 | 描述 |
|--------|----------|----------|------|
| 0 | 超级管理员 | 最高权限 | 拥有系统所有功能权限，可管理所有用户和数据 |
| 1 | 管理员 | 高级权限 | 可访问后台管理功能，权限由具体配置决定 |
| 2 | 普通用户 | 前端权限 | 只能访问前端系统，无后台管理权限 |
| 3 | 待审核用户 | 无权限 | 注册后等待审核，无法访问任何系统功能 |

### 部门体系与ID生成规则

系统支持多部门管理，每个部门有独特的ID前缀：

| 部门名称 | 前缀代码 | ID格式 | 示例 |
|----------|----------|--------|------|
| 总经办 | 10 | 10YYYYMMDD0001 | 10202501010001 |
| 电商部 | 11 | 11YYYYMMDD0001 | 11202501010001 |
| 新媒体部 | 12 | 12YYYYMMDD0001 | 12202501010001 |
| 销售部 | 13 | 13YYYYMMDD0001 | 13202501010001 |
| 技术部 | 14 | 14YYYYMMDD0001 | 14202501010001 |
| 设计部 | 15 | 15YYYYMMDD0001 | 15202501010001 |
| 施工部 | 16 | 16YYYYMMDD0001 | 16202501010001 |
| 访客 | 19 | 19YYYYMMDD0001 | 19202501010001 |

**ID规则说明**:
- 前2位：部门代码
- 中间8位：注册日期(YYYYMMDD)
- 后4位：当日序号(0001-9999)

### 双权限表设计

系统采用前后端分离的权限管理模式，通过两个权限表进行精细化控制：

#### 后台权限表 (user_permissions_backend)

控制后台管理系统的页面访问权限：

| 权限字段 | 功能说明 | 权限值说明 |
|----------|----------|------------|
| `user_review` | 用户审核页面 | 0=有权限，1=无权限 |
| `review_all` | 审核所有用户 | 0=有权限，1=无权限 |
| `review_group` | 审核本组用户 | 0=有权限，1=无权限 |
| `user_management` | 用户管理页面 | 0=有权限，1=无权限 |
| `manage_all` | 管理所有用户 | 0=有权限，1=无权限 |
| `manage_group` | 管理本组用户 | 0=有权限，1=无权限 |
| `permission_manage` | 权限管理 | 0=有权限，1=无权限 |
| `form_preset` | 表单预设页面 | 0=有权限，1=无权限 |
| `schedule_management` | 排班管理页面 | 0=有权限，1=无权限 |
| `distribution_rules` | 分发规则页面 | 0=有权限，1=无权限 |
| `distribution_plan` | 分发计划页面 | 0=有权限，1=无权限 |

#### 前端权限表 (user_permissions_frontend)

控制前端业务系统的功能权限：

| 权限分类 | 权限字段 | 功能说明 |
|----------|----------|----------|
| **线索表权限** | `lead_table` | 线索表访问权限 |
| | `check_all_lead` | 查看所有线索 |
| | `check_member_lead` | 查看组员线索 |
| | `check_mine_lead` | 查看自己线索 |
| | `edit_all_lead` | 编辑所有线索 |
| | `edit_member_lead` | 编辑组员线索 |
| | `edit_mine_lead` | 编辑自己线索 |
| | `forbid_edit` | 禁止编辑 |
| | `delete_all_lead` | 删除所有线索 |
| | `delete_member_lead` | 删除组员线索 |
| | `delete_mine_lead` | 删除自己线索 |
| | `export_lead_table` | 导出线索表 |
| | `lead_submit` | 提交线索 |
| **回收站权限** | `recycle_bin` | 回收站访问权限 |
| | `check_all_bin` | 查看所有回收站数据 |
| | `check_member_bin` | 查看组员回收站数据 |
| | `check_mine_bin` | 查看自己回收站数据 |
| **消息权限** | `user_messages` | 用户消息功能 |
| | `check_all_messages` | 查看所有消息 |
| | `check_member_messages` | 查看组员消息 |
| | `check_mine_messages` | 查看自己消息 |
| **CRM权限** | `lead_crm` | 客户关系管理功能 |
| | `check_person_crm` | 查看个人线索 |
| | `check_group_crm` | 查看组内线索 |
| | `check_all_crm` | 查看全部线索 |

### 销售组与销售权限接口

#### POST /api/leads_xiaoshou - 获取销售组与销售人员

**功能描述**：根据用户权限动态返回可访问的销售人员信息，用于前端下拉框等组件的数据源。支持多种权限类型切换。

**权限类型**：
- `type=0`: CRM权限模式 (check_all_crm, check_group_crm, check_person_crm)
- `type=1`: 消息权限模式 (check_all_messages, check_member_messages, check_mine_messages)
- `type=2`: 其他权限模式（预留）

**权限级别**（按优先级）：
1. 个人权限 == 0: 返回空数组（仅查看个人数据）
2. 组权限 == 0: 返回当前用户所在组的成员  
3. 全部权限 == 0 或用户为"超管": 返回所有销售部用户，按组分组

**请求参数**：
```json
{
  "type": 0  // 权限类型：0=CRM权限，1=消息权限，2=其他权限（预留）
}
```

**响应格式**：
```json
[
  {
    "value": "销售一组",
    "label": "销售一组", 
    "children": [
      {
        "value": "zhangsan",
        "label": "张三"
      },
      {
        "value": "lisi", 
        "label": "李四"
      }
    ]
  }
]
```

**数据结构说明**：
- 第一层：`value`和`label`为组名（`group_name`）
- 第二层：`value`为用户账号（`account`），`label`为用户姓名（`name`）
- 未分组用户自动归类到"未分组"

**权限验证逻辑**：
- 查询`UserPermissionFrontend`表获取用户权限配置
- 按权限优先级返回相应范围的销售人员数据
- 仅返回销售部门用户，按组名和姓名排序

### 权限验证机制

#### 1. 身份验证流程

```mermaid
graph TD
    A[用户请求] --> B[提取Token]
    B --> C{Token存在?}
    C -->|否| D[返回401未授权]
    C -->|是| E[验证Token格式]
    E --> F{Token有效?}
    F -->|否| D
    F -->|是| G[获取用户信息]
    G --> H{用户存在?}
    H -->|否| D
    H -->|是| I[检查用户状态]
    I --> J{审核状态?}
    J -->|待审核| K[返回403审核中]
    J -->|已审核| L[权限检查]
```

#### 2. 权限检查流程

```mermaid
graph TD
    A[页面/API请求] --> B{超级管理员?}
    B -->|是| C[允许访问]
    B -->|否| D{普通用户?}
    D -->|是| E{前端请求?}
    E -->|否| F[拒绝访问]
    E -->|是| G[检查前端权限表]
    D -->|否| H[查询后台权限表]
    H --> I{权限值=0?}
    I -->|是| C
    I -->|否| F
    G --> J{权限值=0?}
    J -->|是| C
    J -->|否| F
```

#### 3. Token生成与验证

- **Token格式**: `user_{用户ID}`
- **存储位置**: Cookie 或 Authorization Header
- **验证方式**: 提取用户ID后查询数据库验证用户状态

### 权限配置管理

#### 权限值规则
- **0**: 有权限（允许访问）
- **1**: 无权限（默认值，拒绝访问）

#### 权限继承规则
1. **超级管理员**: 忽略权限表，拥有所有权限
2. **普通用户**: 仅能访问前端系统，不检查后台权限
3. **管理员**: 根据权限表进行详细权限检查

#### 权限查询逻辑
```sql
-- 查询用户权限
SELECT * FROM user_permissions_backend 
WHERE department = '用户部门' 
AND identity = '用户身份';
```

### API权限控制

#### 公开API（无需认证）
- `/` - 登录页面
- `/api/login` - 用户登录
- `/api/register` - 用户注册
- `/static/*` - 静态资源
- `/api/presets*` - 表单预设相关

#### 普通用户可访问API
```
/api/leads/*              # 线索管理
/api/presets*            # 表单预设
/api/users/info           # 用户信息
/api/upload/withdraw_evidence  # 上传撤回凭证
/api/send_email           # 发送邮件
/api/user/profile         # 用户资料
/api/permissions/frontend # 前端权限
/api/recycle-bin/*        # 回收站
/api/update-crm-permissions # CRM权限管理（需要实现）
```

#### 管理员专用API
```
/api/pending-users        # 待审核用户
/api/approve-user/*       # 批准用户
/api/reject-user/*        # 拒绝用户
```

### 权限配置示例

#### 示例1: 电商部组长权限配置
```json
{
  "department": "电商部",
  "identity": "组长",
  "user_review": 1,        // 无用户审核权限
  "user_management": 0,    // 有用户管理权限
  "manage_all": 1,         // 无法管理所有用户
  "manage_group": 0,       // 可管理本组用户
  "form_preset": 0,        // 有表单预设权限
  "schedule_management": 0, // 有排班管理权限
  "distribution_rules": 0,  // 有分发规则权限
  "distribution_plan": 0    // 有分发计划权限
}
```

#### 示例2: 技术部开发人员权限配置
```json
{
  "department": "技术部",
  "identity": "开发人员",
  "user_review": 1,        // 无用户审核权限
  "user_management": 1,    // 无用户管理权限
  "form_preset": 0,        // 有表单预设权限
  "schedule_management": 1, // 无排班管理权限
  "distribution_rules": 0,  // 有分发规则权限（开发调试）
  "distribution_plan": 1    // 无分发计划权限
}
```

## 🔧 配置说明

### 数据库配置
- 支持连接池配置
- 自动重连机制
- 连接保活功能

### 安全配置
- JWT Token 认证
- 密码 bcrypt 哈希
- 敏感数据 Fernet 加密
- CORS 跨域配置

### 权限配置文件
权限配置通过数据库表进行管理，支持动态调整：

```python
# 页面权限映射
PAGE_PERMISSIONS = {
    "user_review": "用户审核页面",
    "user_management": "用户管理页面", 
    "form_preset": "表单预设页面",
    "schedule_management": "排班管理页面",
    "distribution_rules": "分发规则页面",
    "distribution_plan": "分发计划页面"
}
```

## 📚 API 文档

启动应用后，访问以下地址查看 API 文档：
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

### 主要 API 端点

- `/api/leads/*` - 线索管理相关API
- `/api/users/*` - 用户管理API
- `/api/schedule/*` - 排班管理API
- `/api/distribution/*` - 分发管理API
- `/api/monitoring/*` - 监控API
- `/api/task/*` - 自动化任务管理API

### PUT /api/distribution/rules/{rule_id}

#### 功能描述
用于编辑和更新销售排班规则的接口。支持修改排班的各项信息，包括时间范围、期望接待数等。

#### 请求方法
- `PUT`

#### 请求参数
```json
{
    "channel_type": "电商渠道",
    "group_name": "销售一组",
    "leader": "张三",
    "member": "李四",
    "shift": "早班",
    "expected_reception": 10,
    "expected_free_reception": 6,
    "expected_paid_reception": 4,
    "store": "旗舰店",
    "time_range_start": "09:00",  // 开始时间
    "time_range_end": "12:00",    // 结束时间
    "remarks": "备注信息"
}
```

#### 业务逻辑说明

**时间范围处理优化（v1.2.0）**：
- `time_range_start` 和 `time_range_end` 字段现在基于排班对应的日期（`DistributionRule.date`）生成完整时间戳
- 解决了在今天修改未来日期排班时时间戳不正确的问题
- 时间戳生成规则：`排班日期 + 时间段 = 完整时间戳`

**示例**：
```python
# 假设编辑的是2025-06-20的排班，时间段为09:00-12:00
DistributionRule.date = "2025-06-20"
time_range = "09:00-12:00"

# 生成的时间戳
time_range_start = "2025-06-20 09:00:00"
time_range_end = "2025-06-20 12:00:00"
```

#### 字段映射关系
- `expected_reception` → `expected_total`
- `expected_free_reception` → `expected_free`
- `expected_paid_reception` → `expected_paid`
- `remarks` → `remark`
- `time_range_start` + `time_range_end` → `time_range` + `time_range_start` + `time_range_end`

#### 注意事项
- 修改排班信息后会自动重新计算同组的在岗人数
- 时间戳生成现在基于排班日期而非当前日期，确保跨日期编辑的准确性
- 涉及的数据表：`DistributionRule`
- 支持部分字段更新，未传入的字段保持原值不变

### PUT /api/shenpi_cehui

#### 功能描述
用于处理撤回申请的审批接口。

#### 请求方法
- `PUT`

#### 业务逻辑说明

当 `withdraw_status = 1`（表示审核通过）时：
- 将 `ClueWithdraw.contact_person` 的值追加到 `ClueSheet.not_admin` 字段中；
- 多个联系人之间以英文逗号 `,` 分隔；
- 若 `not_admin` 已有内容，则新内容接在其后，格式为：`原有内容,contact_person`；
- 本接口仅执行上述逻辑，不涉及其他新增或修改操作。

#### WebSocket推送功能 (2025-06-30)
审批操作成功后会自动触发WebSocket推送通知：
- **推送接口**：`/api/websocket/shenpi_cehui/{lead_id}`
- **推送时机**：审批状态更新成功后立即推送
- **推送内容**：包含审批结果（通过/驳回）、lead_id等相关信息
- **实现方式**：使用httpx.AsyncClient进行HTTP接口调用，保持松耦合
- **错误处理**：推送失败不影响审批操作的正常完成
- **超时设置**：15秒超时，避免长时间阻塞

#### 注意事项
- 确保字段拼接时不出现多余的逗号；
- 仅在审核通过时执行追加操作；
- 涉及的数据表：`ClueWithdraw` 和 `ClueSheet`；
- WebSocket推送采用异步调用，不会影响主业务流程。

### POST /api/distribution/queue/save

#### 功能描述
用于保存分发队列数据到数据库的接口。支持保存免费队列和付费队列，同时优化了队列生成的时间排序逻辑。

#### 请求方法
- `POST`

#### 请求参数
```json
{
    "queue_date": "2025-01-20",
    "channel_type": "电商渠道",
    "free_queue": [
        {
            "position": 1,
            "group_name": "销售一组",
            "leader": "张三",
            "member": "李四",
            "time_slot": "09:00-12:00",
            "status": "pending",
            "remark": ""
        }
    ],
    "paid_queue": [
        {
            "position": 1,
            "group_name": "销售二组", 
            "leader": "王五",
            "member": "赵六",
            "time_slot": "14:00-17:00",
            "status": "pending",
            "remark": ""
        }
    ]
}
```

#### 业务逻辑说明

**队列排序优化（v1.2.1）**：
- 在生成分发队列时，现在优先处理 `DistributionRule` 表中 `time_range_start`（时间段开始时间）较早的规则
- 应用于两种分发算法：
  - **交替配额轮循法**：按时间段开始时间排序后进行阶段性分配
  - **动态权重轮循法**：在权重计算基础上，优先处理较早时间段的规则
- 确保早班优先、合理安排人员分发顺序

#### 核心功能
1. **状态恢复机制**：保持已分发队列项的状态和分发信息
2. **时间戳处理**：自动生成 `time_slot_beg` 和 `time_slot_end` 时间戳
3. **后台任务触发**：保存完成后自动处理待分发线索
4. **数据一致性**：使用事务确保数据操作的原子性

#### 响应格式
```json
{
    "status": "success",
    "message": "队列保存成功，已开始处理待分发线索"
}
```

#### 注意事项
- 队列排序现在基于 `time_range_start` 字段，优先处理较早的时间段
- 保存前会删除现有队列数据，但保留已分发项的状态信息
- 涉及的数据表：`DistributionRule`、`DistributionQueue`
- 支持免费队列和付费队列的独立管理

### PUT /api/distribution/queue/update

#### 功能描述
智能队列更新接口，用于更新现有分发队列。实现保持已分发队列不变、智能处理队列增减场景，并立即应用相邻约束交叉排序。

#### 请求方法
- `PUT`

#### 请求参数
```json
{
    "queue_date": "2025-06-25",      // 队列日期（必填）
    "channel_type": "电商渠道"        // 渠道类型（必填：电商渠道、新媒体渠道）
}
```

#### 核心功能
1. **保持已分发队列不变**：所有状态为"已分发"的队列记录保持原有序号和分配关系
2. **智能队列增加**：在原有队列基础上新增队列项，使用交叉排列算法分配
3. **智能队列减少**：不减少已分发队列，仅为需要增加的成员分配新的待分发队列
4. **立即排序应用**：更新完成后立即应用相邻约束交叉排序并返回结果

#### 业务逻辑说明

**队列更新算法（v1.2.8 - 修复增量计算错误）**：
- **🔧 关键修复**：统计基准从"已分发队列"改为"所有现有队列"
  * 修复前：只统计已分发队列数量，忽略待分发队列
  * 修复后：统计所有现有队列数量（已分发+待分发）
- **按班次统计现有队列总数**：按 `成员+时间段` 组合键统计所有现有队列数量
  * 统计键格式：`"成员A_09:00-18:30"`, `"成员A_18:30-23:59"` 等
  * 确保每个成员的每个班次独立统计，避免跨班次计算错误
- **真正增量计算**：每个规则独立计算真实增量
  * 修复前公式：新规则预期数量 - 该成员该时间段已分发数量（错误）
  * 修复后公式：新规则预期数量 - 该成员该时间段现有队列总数（正确）
  * 场景验证：2人×2班次×2条=8条队列，白班改为3条时，正确增加2条而非6条
- **智能增减处理**：
  * 队列增加：在原队列基础上新增队列项，确保增量准确
  * 队列减少：删除多余的待分发队列项，但保持已分发队列不变
  * 保护机制：已分发队列永远不被修改或删除
- **交叉排序应用**：使用 `sort_queue_by_shift_groups` 函数重新排序所有队列

#### 响应格式
```json
{
    "success": true,
    "message": "成功更新 电商渠道 分发队列，已应用相邻约束交叉排序",
    "queue_date": "2025-06-25",
    "channel_type": "电商渠道",
    "free_queue": [...],              // 更新后的免费队列（已排序）
    "paid_queue": [...],              // 更新后的付费队列（已排序）
    "total_free": 15,
    "total_paid": 8,
    "total_items": 23,
    "algorithm_description": "智能队列更新算法：保持已分发队列不变，根据新规则智能计算需要新增的队列项，应用相邻约束交叉排序确保队列的合理分布和多样性。"
}
```

#### 修复示例（v1.2.9）
**场景**：2个人员，每人白班+晚班各2条队列，修改白班为3条

**修复前错误逻辑**：
```
成员A统计：已分发4条（白班2+晚班2）
成员A新规则：白班3条+晚班2条=5条
成员A需新增：5-4=1条 ❌（但不知道加到哪个班次）

成员B统计：已分发4条（白班2+晚班2）
成员B新规则：白班3条+晚班2条=5条
成员B需新增：5-4=1条 ❌（但不知道加到哪个班次）

结果：可能错误地增加6条而非2条
```

**修复后正确逻辑**：
```
成员A_09:00-18:00：已分发2条，新规则3条 → 需新增1条 ✅
成员A_18:00-23:59：已分发2条，新规则2条 → 需新增0条 ✅
成员B_09:00-18:00：已分发2条，新规则3条 → 需新增1条 ✅
成员B_18:00-23:59：已分发2条，新规则2条 → 需新增0条 ✅

结果：精确新增2条队列（2人×1条白班增量）
```

#### 注意事项
- 已分发队列项的状态、分发时间、分发人等信息完全保持不变
- 新增队列项会自动分配正确的position值，确保排序连续性
- 支持队列增加和减少两种场景的智能处理
- 涉及的数据表：`DistributionRule`、`DistributionQueue`
- 更新完成后自动触发后台任务处理待分发线索

### 分发规则批量操作API

#### PUT /api/distribution/plrules - 批量修改分发规则

**功能描述**：批量修改多条分发规则的指定字段，支持跨分组选择操作

**请求方法**：`PUT`

**请求参数**：
```json
{
    "ids": [1001, 1002, 1003],           // 要修改的规则ID列表（必填）
    "update_data": {                     // 要修改的字段和值（必填）
        "shift": "晚班",                  // 班次（可选）
        "expected_free_reception": 10,   // 预计免费接待数（可选）
        "expected_paid_reception": 5,    // 预计付费接待数（可选）
        "store": "店铺A",                // 店铺（可选）
        "time_range_start": "18:00:00",  // 开始时间（可选）
        "time_range_end": "23:59:00",    // 结束时间（可选）
        "remarks": "批量修改"             // 备注（可选）
    }
}
```

**字段映射说明**：
- `expected_reception` → `expected_total`
- `expected_free_reception` → `expected_free`
- `expected_paid_reception` → `expected_paid`
- `remarks` → `remark`

**响应格式**：
```json
{
    "message": "成功批量修改 3 条分发规则",
    "updated_count": 3,
    "updated_ids": [1001, 1002, 1003]
}
```

#### DELETE /api/distribution/plrules - 批量删除分发规则

**功能描述**：批量软删除多条分发规则，支持跨分组选择操作

**请求方法**：`DELETE`

**请求参数**：
```json
{
    "ids": [1001, 1002, 1003],           // 要删除的规则ID列表（必填）
    "delete_user": "张三"                // 删除操作用户（可选）
}
```

**响应格式**：
```json
{
    "message": "成功批量删除 3 条分发规则",
    "deleted_count": 3,
    "deleted_ids": [1001, 1002, 1003]
}
```

#### 批量操作特性说明

**支持的操作字段**：
- 班次（shift）
- 预计免费接待数（expected_free）
- 预计付费接待数（expected_paid）
- 店铺（store）
- 时间范围（time_range_start、time_range_end）
- 备注（remarks）

**核心特性**：
- **跨分组选择**：支持同时选择多个分组中的规则进行操作
- **软删除机制**：删除操作使用软删除，数据可恢复
- **智能更新**：自动重新计算受影响分组的在岗人数
- **数据一致性**：使用数据库事务确保操作的原子性
- **兼容现有功能**：与单条操作接口保持一致的字段映射和业务逻辑

**注意事项**：
- 只操作未删除的记录（`is_deleted = false`）
- 批量修改会自动更新 `updated_at` 时间戳
- 删除操作会记录删除时间和删除用户
- 受影响分组的在岗人数会自动重新计算

### POST /api/distribution/media/auto-dispatch

#### 功能描述
用于自动分发线索的接口。

#### 请求方法
- `POST`

#### 业务逻辑说明

当 `ClueSheet.not_admin` 字段存在值时（多个值以英文逗号 `,` 分隔）：
- 在线索分发过程中，过滤掉 `DistributionQueue.member` 值存在于 `not_admin` 列表中的成员；
- 即：不将线索分发给 `not_admin` 中列出的成员；
- 该过滤逻辑适用于所有分发场景：店铺匹配分发、常规队列分发、队列转移分发；
- 本接口仅执行上述逻辑，不涉及其他新增或修改操作。

#### 注意事项
- 确保对 `not_admin` 字段进行正确拆分并转换为可匹配的查询条件；
- 处理空值、空格等边界情况；
- 涉及的数据表：`ClueSheet` 和 `DistributionQueue`；
- 排除逻辑在三种分发场景中均生效。

### GET /api/distribution/rules2

#### 功能描述
获取分发规则列表并实时更新实际接待数量统计信息。

#### 请求方法
- `GET`

#### 查询参数
- `rule_date` (可选): 规则日期，格式为 YYYY-MM-DD  
- `channel_type` (可选): 渠道类型，如 "电商渠道", "新媒体渠道"

#### 新增功能（v2.1）
- 新增对字段 `actual_total`、`actual_free` 和 `actual_paid` 的统计计算
- 统计依据为 `ClueSheet` 与 `SalaCrm` 的联表查询
- 更新逻辑仅修改指定字段，不对其他数据产生影响

#### 查询条件说明

| 字段名         | 查询条件 |
|----------------|----------|
| actual_total   | ClueSheet.status ∈ [3,4]，DistributionRule.date = SalaCrm.allocation_date，DistributionRule.member = ClueSheet.contact_person |
| actual_free    | 同上 + ClueSheet.queue = '0' |
| actual_paid    | 同上 + ClueSheet.queue = '1' |

#### 表关联关系
- `ClueSheet` 与 `SalaCrm` 通过 `ClueSheet.ID == SalaCrm.ClueID` 关联
- `DistributionRule` 与 `ClueSheet` 通过 `DistributionRule.member == ClueSheet.contact_person` 关联
- `DistributionRule.date == SalaCrm.allocation_date` 为通用时间条件

#### 响应数据示例
```json
[
  {
    "id": 1,
    "date": "2024-01-20",
    "channel_type": "电商渠道",
    "group_name": "销售一组",
    "member": "张三",
    "expected_total": 10,
    "expected_free": 6,
    "expected_paid": 4,
    "actual_total": 8,
    "actual_free": 5,
    "actual_paid": 3,
    "updated_at": "2024-01-20T10:30:00"
  }
]
```

#### 优化特性
- ✅ 实时计算并更新实际接待数量统计
- ✅ 支持批量处理提高性能
- ✅ 事务处理确保数据一致性
- ✅ 详细的日志记录便于调试
- ✅ 完善的错误处理和回滚机制

#### 注意事项
- 接口在返回数据前会自动计算并更新实际接待数统计
- 使用数据库事务确保数据一致性
- 异常情况下会自动回滚避免数据不一致
- 涉及的数据表：`ClueSheet`、`DistributionRule`、`SalaCrm`

### 自动化任务管理API

#### POST /api/task/set-time

**功能描述**：设置任务时间，并重新调度定时任务（适配前端页面）

**请求参数**：
```json
{
    "time": "09:30",                      // 任务时间（必填，格式：HH:MM）
    "date": "2025-01-20"                  // 任务日期（可选，格式：YYYY-MM-DD，默认今天）
}
```

#### POST /api/task/toggle-status

**功能描述**：控制任务开关，开启或关闭定时任务

**请求参数**：
```json
{
    "status": 1  // 任务状态，0=关闭，1=开启
}
```

#### GET /api/task/info

**功能描述**：查询指定日期的任务信息

**查询参数**：
- `date_riqi` (必填): 日期字符串，格式：YYYY-MM-DD

#### GET /api/task/today

**功能描述**：获取今天的任务信息（用于页面初始化）

**响应格式**：
```json
{
    "success": true,
    "message": "获取今天任务信息成功",
    "data": {
        "id": 1,
        "date_time": "2025-01-20T09:00:00",
        "status": 1,
        "formatted_time": "09:00",
        "task_running": true
    }
}
```

#### GET /api/task/status

**功能描述**：获取当前任务运行状态

#### POST /api/task/manual-trigger

**功能描述**：手动触发队列生成任务（用于测试）

**自动化任务功能特点**：
- ✅ 基于 `BackgroundTasks` + `time.sleep()` 的定时任务调度
- ✅ 支持实时修改任务启动时间并重新调度
- ✅ 动态开关控制，可随时启停任务
- ✅ 每天只允许一条任务记录，确保数据唯一性
- ✅ 任务到时自动生成电商渠道和新媒体渠道的分发队列
- ✅ 完善的错误处理和状态监控
- ✅ 前后端完全集成，状态实时同步

## 🔍 监控与日志

### 系统监控
- 数据库连接状态监控
- 队列状态监控
- 分发效果监控

### 日志记录
- 请求日志
- 错误日志
- 数据库操作日志

## 🛠️ 开发指南

### 代码结构
- 路由层 (`routers/`): 处理HTTP请求和响应
- 业务逻辑层 (`main.py`): 核心业务逻辑
- 数据访问层 (`models.py`, `database.py`): 数据模型和数据库操作
- 工具层 (`utils/`): 通用工具函数

### 开发规范
- 使用 SQLAlchemy ORM 进行数据库操作
- 敏感数据必须加密存储
- API 使用 RESTful 设计原则
- 完善的错误处理和日志记录

## 🔧 CRM权限管理使用指南

### 功能特点
- **细粒度控制**: 支持个人、组内、全部三个层级的线索查看权限
- **独立API**: 通过专用API接口进行权限管理，与其他权限分离
- **实时生效**: 权限修改后立即生效，无需重启系统

### 使用步骤

1. **访问权限管理页面**
   - 登录后台管理系统
   - 进入"用户管理" → "权限管理"
   - 切换到"前端权限"选项卡

2. **配置CRM权限**
   - 找到"客户关系管理"列
   - 点击权限按钮旁的三角形图标
   - 在弹出窗口中配置具体权限：
     - 查看个人线索：用户只能查看自己创建的线索
     - 查看组内线索：用户可以查看同组成员的线索
     - 查看全部线索：用户可以查看所有线索

3. **权限说明**
   - 绿色按钮：允许该权限
   - 红色按钮：禁止该权限
   - 黄色按钮：部分权限（主按钮状态）

### API接口

#### 更新CRM权限
```http
POST /api/update-crm-permissions
Content-Type: application/json

{
    "permission_id": "权限记录ID",
    "permissions": {
        "check_person_crm": 0,     // 查看个人线索 (0=允许, 1=禁止)
        "check_group_crm": 1,   // 查看组内线索 (0=允许, 1=禁止)
        "check_all_crm": 1       // 查看全部线索 (0=允许, 1=禁止)
    }
}
```

#### 获取CRM权限
```http
GET /api/crm-permissions/{permission_id}
```

### 数据库迁移

如需添加CRM权限字段到现有数据库，请运行：
```bash
python add_crm_permissions.py
```

该脚本会：
- 为 `user_permissions_frontend` 表添加三个CRM权限字段
- 设置默认值为1（禁止）
- 检查字段是否已存在，避免重复添加

## 🚨 注意事项

1. **数据安全**: 手机号、微信号等敏感信息会自动加密存储
2. **权限控制**: 严格的分级权限管理，确保数据安全
3. **连接池**: 数据库连接池配置需根据实际负载调整
4. **备份**: 定期备份数据库，确保数据安全
5. **CRM权限**: 新增的CRM权限需要运行数据库迁移脚本

## 📝 更新日志

- **v2.6.4**: 修复导入数据行索引问题 (2025-07-09)
  - **🐛 问题修复**：
    * **数据丢失问题**：修复导入时跳过第一行数据的问题
    * **索引计算错误**：修正start_row的pandas索引计算逻辑
  - **🔧 技术修复**：
    * **索引修正**：将`start_row - 1`修正为`max(0, start_row - 2)`
    * **范围计算**：同时修正end_row的计算逻辑，确保数据范围正确
    * **完整导入**：现在能正确导入Excel中的所有数据行
  - **✅ 验证结果**：
    * ✅ 数据完整性：所有Excel数据行都能正确导入，不再丢失第一行
    * ✅ 索引准确性：pandas索引计算正确，从第一条数据开始处理
    * ✅ 功能兼容性：修复不影响现有的验证功能（主播、店铺、时间格式等）

- **v2.6.3**: 增加店铺验证功能 (2025-07-09)
  - **🎯 核心功能**：为Excel导入功能添加店铺名称验证
  - **📊 数据来源整合**：
    * **新媒体店铺**：从config/presets.json的shops中提取douyin_abbr、video_abbr、xiaohongshu_abbr、kuaishou_abbr字段
    * **电商店铺**：从config/ecommerce_stores.json的stores中提取所有店铺名称
    * **数据合并**：将两个来源的店铺名称合并并去重，形成完整的有效店铺列表
  - **🔧 技术实现**：
    * **动态读取**：实时从配置文件读取最新的店铺列表
    * **严格验证**：导入时验证每行的店铺字段是否在有效列表中
    * **错误定位**：提供具体的行号、微信ID和无效店铺名称
    * **集成报告**：错误信息集成到现有的验证报告系统中
  - **✅ 测试验证**：
    * ✅ 新媒体店铺验证：正确识别有效的抖音、视频号、小红书、快手店铺简称
    * ✅ 电商店铺验证：正确识别有效的淘宝、京东等电商店铺名称
    * ✅ 无效店铺检测：准确检测并报告无效店铺名称（如"抖-小甜5"、"抖-小甜33"）
    * ✅ 错误信息准确：提供具体的店铺名称和位置信息，便于操作员修正

- **v2.6.2**: 修复预览API错误和增强数据处理 (2025-07-09)
  - **🐛 问题修复**：
    * **预览API 500错误**：修复Excel中NaN值导致的JSON序列化错误
    * **数据类型处理**：增强对各种Excel数据类型的处理能力
    * **字符串清理**：自动清理换行符和多余空格
  - **🔧 技术改进**：
    * **NaN值处理**：所有NaN值统一转换为None，确保JSON序列化正常
    * **时间格式化**：自动转换为ISO格式字符串，便于前端显示
    * **数字格式化**：正确处理电话号码和微信ID的格式
    * **错误信息优化**：提供详细的行号、微信ID和具体错误描述
  - **✅ 验证结果**：
    * ✅ 预览功能正常：能正确处理包含错误数据的Excel文件
    * ✅ 错误检测准确：正确识别登记人为空、对接人无效、主播无效、时间为空等错误
    * ✅ 数据清理有效：自动处理换行符、NaN值、时间格式等问题
    * ✅ 用户体验友好：提供具体的错误位置和原因，便于操作员修正数据

- **v2.6.1**: 增加主播字段验证和时间格式优化 (2025-07-09)
  - **🎯 核心功能**：为Excel导入功能添加主播字段验证和智能时间格式处理
  - **📊 字段更新**：
    * **新增必填字段**：主播 - 从config/presets.json的hosts配置中读取有效主播名单
    * **验证逻辑**：导入时验证主播名是否在预设的主播名单中，不匹配则报错
    * **配置来源**：主播名单从presets.json文件的hosts数组中动态读取
  - **⏰ 时间格式优化**：
    * **多格式支持**：支持多种时间格式输入（2025/7/7 16:59:47、2025-07-07 16:59:47、2025/7/7 16:59等）
    * **智能解析**：使用pandas智能解析功能作为备选方案
    * **标准存储**：所有时间统一转换为 `2025-06-25 01:29:47` 格式存储到数据库
    * **容错处理**：解析失败时使用当前时间，确保导入不中断
  - **🔧 技术实现**：
    * **动态读取**：从config/presets.json读取hosts配置，支持逗号分隔的主播名
    * **实时验证**：导入时实时验证主播名有效性，提供详细错误信息
    * **时间转换**：多层级时间格式解析，确保各种Excel时间格式都能正确处理
    * **错误处理**：配置文件读取失败时自动设置空名单，确保验证严格性
  - **✅ 测试验证**：
    * ✅ 主播名单读取：成功从presets.json读取20个有效主播名
    * ✅ 验证功能正常：有效主播通过验证，无效主播正确报错
    * ✅ 时间格式处理：支持多种时间格式，统一转换为标准格式存储
    * ✅ 错误信息准确：提供具体的主播名和错误描述
    * ✅ 预览功能兼容：预览接口正确识别主播字段和各种时间格式

- **v2.6.0**: 完成Excel导入功能开发和测试 (2025-07-05)
  - **🎯 核心功能**：实现完整的Excel线索导入功能，支持预览→确认→导入的完整工作流
  - **📊 导入接口**：
    * **预览接口**：`POST /api/leads/preview_import_xiansuo` - 上传Excel文件并预览数据
    * **导入接口**：`POST /api/leads/import_xiansuo` - 执行实际的数据导入操作
  - **🔧 技术实现**：
    * **字段映射**：支持中文字段名到英文字段名的自动映射
    * **数据验证**：必填字段验证（记录日期、微信ID、微信昵称、店铺、登记人）
    * **ID生成**：基于日期的唯一ID生成，格式：YYYYMMDD000001
    * **双表插入**：同时插入ClueSheet和SalaCrm表，保持数据一致性
    * **敏感数据加密**：自动加密手机号、微信号等敏感信息
  - **📋 支持字段**：
    * **必填字段**：记录日期、渠道、微信ID、微信昵称、店铺、登记人、对接人、记录时间、主播
    * **可选字段**：渠道ID、电话号码、备注、班次
    * **自动字段**：queue（根据店铺名是否包含"F"自动设置）、type（根据是否有对接人自动设置）
  - **🎯 业务逻辑**：
    * **店铺分类**：包含"F"的店铺设为免费队列(queue=0)，否则为付费队列(queue=1)
    * **线索类型**：有对接人的设为被动线索(type=1)，否则为默认线索(type=0)
    * **分配日期**：有对接人的线索自动设置allocation_date为当前时间
    * **状态管理**：导入的线索状态默认为1（已提交）
  - **✅ 测试验证**：
    * ✅ 预览功能正常：正确解析Excel文件并返回列名和数据
    * ✅ 导入功能正常：成功导入9条真实测试数据，无错误
    * ✅ 字段映射正确：中文字段名正确映射到数据库字段，兼容Excel模板格式
    * ✅ 数据验证有效：必填字段验证和数据格式验证正常
    * ✅ ID生成唯一：每条记录生成唯一的递增ID
    * ✅ 双表插入成功：ClueSheet和SalaCrm表数据同步插入
    * ✅ 前端集成完成：前端API调用地址已更新，支持完整导入流程
    * ✅ 真实模板测试：使用用户提供的Excel模板文件测试成功
  - **📁 测试文件**：
    * 使用用户提供的真实Excel模板：`新系统客户导入格式-测试数据.xlsx`
    * 模板包含10条真实测试数据，涵盖各种业务场景
    * 字段映射兼容性：支持"渠道ID"→"渠道"、"电话号码"→"电话"等字段名变化
    * 成功导入9条有效数据（跳过1条无效数据）
  - **🔄 前端适配**：
    * 更新前端API调用地址为http://localhost:8001
    * 保持前端导入界面和用户体验不变
    * 支持文件上传→数据预览→确认导入的完整流程
  - **📚 文档更新**：详细记录导入功能的实现细节、字段映射规则和测试结果

- **v2.5.4**: 优化文件上传接口支持多文件上传 (2025-07-11)
  - **🔧 核心优化**：优化 `/api/upload/withdraw_evidence` 接口支持多文件上传
  - **🎯 功能增强**：
    * **多文件支持**：单次请求可上传多张图片文件（最多10个）
    * **返回格式**：文件路径和文件名以英文逗号分隔的字符串格式返回
    * **向后兼容**：完全兼容单文件上传，保持现有功能不变
    * **文件验证**：支持图片格式验证（jpg, jpeg, png, gif, bmp, webp）
    * **大小限制**：单文件最大10MB限制，总文件数量最多10个
    * **错误处理**：完善的多文件上传错误处理机制
  - **📊 接口变更**：
    ```python
    # 请求参数：files: List[UploadFile] = File(...)
    # 返回格式：{
    #   "success": True,
    #   "message": "文件上传成功，共上传 2 个文件",
    #   "file_path": "path1.jpg,path2.jpg",  # 逗号分隔字符串
    #   "filename": "file1.jpg,file2.jpg",   # 逗号分隔字符串
    #   "file_count": 2,                     # 新增：文件数量
    #   "files": ["path1.jpg", "path2.jpg"]  # 新增：文件列表
    # }
    ```
  - **🔄 命名规则**：
    * 单文件：`withdraw_20250711143022.jpg`
    * 多文件：`withdraw_20250711143022_01.jpg, withdraw_20250711143022_02.jpg`
  - **🧪 测试工具**：创建 `test_multi_upload.html` 测试页面，支持多文件选择和上传测试
  - **🔧 问题修复**：修复多文件上传接口的错误处理问题（return → raise HTTPException）
  - **🔄 兼容性增强**：新增 `/api/upload/withdraw_evidence_single` 单文件兼容接口
  - **📋 测试验证**：创建 `test_upload_detailed.html` 详细测试页面，验证各种上传场景

- **v2.5.5**: 修复self_reminder_flag历史记录保存问题 (2025-07-15)
  - **🐛 问题修复**：修复"再次跟进提醒记录"第一次修改不保存到sala_crm_history表的问题
  - **🔧 根本原因**：
    * 原代码逻辑错误：先调用add_history_data，只有返回True才更新主表字段
    * 当add_history_data因空值等原因返回False时，主表字段不更新，导致第二次修改条件仍然满足
  - **✅ 解决方案**：
    * **业务逻辑优化**：先更新主表字段，再保存历史记录
    * **错误隔离**：历史记录保存失败不影响主要业务逻辑
    * **空值处理**：改进add_history_data函数的空值处理逻辑
  - **🎯 修复效果**：
    * 第一次修改self_reminder_flag即可成功保存
    * 历史记录保存更加稳定可靠
    * 主要业务逻辑与历史记录功能解耦
  - **🧪 测试工具**：创建 `test_self_reminder_fix.py` 验证修复效果

- **v2.5.6**: 修复客情记录字段被意外覆盖问题 (2025-07-15)
  - **🐛 问题描述**：修改微信昵称或其他字段时，客情记录(customer_record)和客情图片(customer_record_images)被意外清空
  - **🔍 根本原因**：
    * 代码逻辑错误：对所有字段都使用 `update_data.get()` 获取值
    * 当前端只传递部分字段时，未传递的字段返回 `None`
    * 错误的比较逻辑导致 `None` 值覆盖数据库中的有效数据
  - **✅ 解决方案**：
    * **字段存在性检查**：使用 `"field_name" in update_data` 检查字段是否被明确传递
    * **条件更新**：只有当前端明确传递了字段时才进行更新操作
    * **数据保护**：未传递的字段保持数据库中的原有值不变
  - **🎯 修复效果**：
    * 修改微信昵称等其他字段时，客情记录保持不变
    * 直接修改客情记录时，功能正常工作
    * 避免了数据的意外丢失
  - **🔧 技术细节**：
    ```python
    # 修复前（错误）
    customer_record = update_data.get("customer_record")  # 可能为None
    if customer_record != db_sala_crm.customer_record:
        db_sala_crm.customer_record = customer_record  # 错误覆盖

    # 修复后（正确）
    if "customer_record" in update_data:  # 检查字段是否存在
        customer_record = update_data.get("customer_record")
        if customer_record != db_sala_crm.customer_record:
            db_sala_crm.customer_record = customer_record  # 安全更新
    ```
  - **🧪 测试工具**：创建 `test_customer_record_fix.py` 验证字段保护功能

- **v2.5.7**: 优化is_billed字段更新返回数据 (2025-07-15)
  - **🎯 需求描述**：当请求数据为`{"ID": "20250714000008", "is_billed": true}`时，返回的`updated_fields`中需要包含`bill_number`和`bill_use_time`字段
  - **📊 期望返回格式**：
    ```json
    {
        "status_code": 1,
        "detail": "更新成功",
        "id": "20250714000008",
        "updated_fields": {
            "ID": "20250714000008",
            "is_billed": 1,
            "bill_number": "订单编号",
            "bill_use_time": 计算的用时天数
        }
    }
    ```
  - **✅ 实现方案**：
    * **自动计算字段包含**：当`is_billed`设置为1时，自动将计算的`bill_use_time`添加到返回数据
    * **现有数据保持**：如果数据库中已有`bill_number`且请求中未传递，将其包含在返回数据中
    * **字段同步更新**：确保`bill_number`字段更新时也正确包含在返回数据中
  - **🔧 技术实现**：
    ```python
    if is_billed == 1:
        # 计算开单用时
        db_sala_crm.bill_riqi = datetime.now()
        delta = db_sala_crm.bill_riqi - db_sala_crm.allocation_date
        total_days = round(delta.total_seconds() / (3600 * 24),1)
        db_sala_crm.bill_use_time = total_days

        # 将自动计算的字段添加到返回数据中
        update_data["bill_use_time"] = total_days
        if "bill_number" not in update_data and db_sala_crm.bill_number:
            update_data["bill_number"] = db_sala_crm.bill_number
    ```
  - **🎯 优化效果**：
    * 前端可以直接从返回数据中获取所有相关字段
    * 减少额外的查询请求
    * 提供完整的更新反馈信息
  - **🧪 测试工具**：创建 `test_is_billed_response.py` 验证返回数据完整性

- **v2.5.8**: 修复公海记录查询接口错误 (2025-07-15)
  - **🐛 问题描述**：`POST /api/sala_crm_openseas` 接口报错，请求数据为 `{"allocation_date": "2025-07-14"}` 时无法正常响应
  - **🔍 错误分析**：
    * **语法错误**：第363行 `filters.SalaCrmOpenseas(condition)` 应为 `filters.append(condition)`
    * **JOIN语句错误**：查询以错误的表作为主表，导致数据关联问题
    * **缺少异常处理**：没有完整的try-catch结构处理错误
    * **导入缺失**：缺少必要的模型类导入
    * **权限验证缺失**：没有用户登录验证
  - **✅ 修复方案**：
    * **语法修复**：修正 `filters.append()` 方法调用
    * **查询优化**：以 `SalaCrmOpenseas` 为主表，左连接 `ClueSheet` 表
    * **异常处理**：添加完整的try-catch-finally结构
    * **导入补全**：添加缺失的 `SalaCrm`, `SalaCrmHistory`, `PostSalaCrm` 导入
    * **权限验证**：添加用户登录状态检查
    * **过滤条件应用**：确保所有过滤条件正确应用到查询中
  - **🔧 技术细节**：
    ```python
    # 修复前（错误）
    filters.SalaCrmOpenseas(condition)  # 语法错误

    # 修复后（正确）
    filters.append(condition)  # 正确的方法调用

    # 查询结构优化
    query = db.query(SalaCrmOpenseas字段...).outerjoin(
        ClueSheet, SalaCrmOpenseas.ID == ClueSheet.ID
    )

    # 添加过滤条件应用
    if filters:
        query = query.filter(and_(*filters))
    ```
  - **🎯 修复效果**：
    * 接口可以正常响应查询请求
    * 支持按日期过滤公海记录
    * 支持关键字搜索和高级过滤
    * 提供完整的错误处理和用户反馈
    * 返回结构化的数据格式
  - **📊 支持功能**：
    * 分页查询：支持page和pageSize参数
    * 日期过滤：按allocation_date精确查询
    * 关键字搜索：跨多字段模糊搜索
    * 高级过滤：支持复杂过滤条件
    * 权限控制：基于用户权限限制数据访问
  - **🧪 测试工具**：创建 `test_openseas_api_fix.py` 验证接口修复效果

- **v2.5.3**: 优化 add_history_data 函数为通用公共方法 (2025-07-03)
  - **🔧 核心优化**：将 `add_history_data` 函数重构为通用的历史记录追加工具
  - **🐛 问题修复**：
    * 修复模型类字符串与实际类对象的映射问题
    * 解决查询记录失败的问题（record_id=20250626000029 等）
    * 修复函数参数类型不匹配导致的查询错误
  - **🎯 功能增强**：
    * **通用性**：支持多种数据表（SalaCrm、ClueSheet、User）
    * **参数优化**：`model_class` 参数支持字符串类型，自动映射到实际模型类
    * **错误处理**：完善的参数验证、记录存在性检查、字段验证
    * **事务安全**：自动提交成功操作，错误时自动回滚
  - **📊 新函数签名**：
    ```python
    def add_history_data(model_class: str, record_id: str, new_value: str, history_field_name: str, db: Session) -> bool
    ```
  - **🔄 向后兼容**：
    * 提供 `add_history_data_legacy` 函数保持原有接口
    * 现有调用代码无需修改即可继续使用
    * 保持原有的JSON格式结构不变
  - **📚 文档完善**：
    * 创建详细的使用指南 `docs/add_history_data_usage.md`
    * 包含完整的示例代码和最佳实践
    * 提供故障排除和性能优化建议
  - **✅ 测试验证**：通过完整的功能测试，确保查询和历史记录追加功能正常

- **v2.5.0**: 实现基于时间段的轮询分发算法重大重构 (2025-07-02)
  - **🎯 核心目标**：完全替换现有的随机分发算法，实现基于时间段开始时间的轮询分发算法，确保时间优先级和公平分配
  - **🔧 算法重构**：
    * 新增 `time_slot_based_polling_algorithm` 基于时间段的轮询分发算法
    * 替换现有的队列生成逻辑，改为按排班开始时间（time_slot_beg）升序排序人员
    * 执行轮询分配：甲→丁→乙→丙为一轮完整循环，重复轮询直到所有数据分配完毕
    * 保留现有的 `sort_queue_with_polling_algorithm` 和 `sort_queue_with_random_algorithm` 作为备选算法
  - **🎯 新算法核心特性**：
    * **时间优先级**：按排班开始时间（time_slot_beg）升序排序所有可用人员
    * **轮询分配**：执行甲→丁→乙→丙为一轮完整循环，确保公平分配
    * **跨渠道隔离**：维持跨渠道隔离（新媒体付费/免费、电商渠道独立处理）
    * **交叉排列约束**：确保相邻队列不能有相同时间段或成员名
    * **分布式队列保护**：保留现有的队列更新逻辑（分布式队列不可变原则）
  - **📊 数据结构示例**：
    * 人员：甲（09:00-18:30）、乙（13:30-23:59）、丙（18:30-23:59）、丁（09:30-23:59）
    * 排序后：甲（09:00）→ 丁（09:30）→ 乙（13:30）→ 丙（18:30）
    * 轮询分配：甲→丁→乙→丙→甲→丁→乙→丙...
  - **⚙️ 配置化管理**：
    * 更新 `QUEUE_ALGORITHM_CONFIG` 配置，将默认算法改为 `time_slot_polling`
    * 新增时间段轮询算法选项，保持与现有算法配置系统兼容
    * 支持在时间段轮询、传统轮询和随机算法之间动态切换
  - **🔄 全面应用**：
    * 自动集成到现有的 `generate_and_save_queue` 函数中
    * 通过配置化的 `get_current_queue_algorithm()` 函数自动应用新算法
    * 保持所有现有API接口不变，确保前端调用无需修改
    * 与现有WebSocket通知系统完全兼容
  - **📊 算法特性**：
    * **时间优先级**：确保早班人员优先分配，提高响应速度
    * **公平性**：轮询算法确保每个成员获得分配机会的公平性
    * **可预测性**：分配结果可预测，便于业务规划和管理
    * **平衡性**：避免同一人连续分发，确保分配的平衡性
    * **兼容性**：保持现有API接口和数据结构不变
  - **🔧 修改文件**：
    * `routers/distribution.py` - 新增时间段轮询算法实现和配置更新
    * `README.md` - 文档更新，包含新算法详细说明和使用指南
  - **✅ 验证结果**：
    * ✅ 时间段排序正确：09:00 → 09:30 → 13:30 → 18:30
    * ✅ 轮询分配正确：甲→丁→乙→丙循环分配
    * ✅ 公平分配：每个成员分配数量相等
    * ✅ 轮询顺序完全正确，前40个项目（10轮完整循环）验证通过
    * ✅ 与现有WebSocket通知系统完全兼容
    * ✅ 所有现有功能保持兼容，无破坏性变更
- **v2.4.0**: 实现轮询（Polling）算法替换随机分配算法 (2025-07-02)
  - **🎯 核心目标**：将现有的随机分配算法替换为轮询（polling）算法，实现更公平和可预测的队列分配
  - **🔧 算法重构**：
    * 新增 `sort_queue_with_polling_algorithm` 轮询排序算法
    * 保留 `sort_queue_with_random_algorithm` 作为备选算法（标记为已弃用）
    * 优化 `dynamic_weighted_round_robin` 和 `phased_quota_round_robin` 算法，组内使用轮询分配
  - **🎯 轮询算法规则**：
    * 按时间段进行优先级排序（时间优先级）
    * 在相同时间段内，按照 A→B→C→A→B→C 的顺序循环分配
    * 跨时间段时，保持轮询顺序的连续性
    * 确保相邻队列不能有相同的 time_slot_beg 或 member_name（交叉排列约束）
  - **⚙️ 配置化管理**：
    * 新增队列分发算法配置系统，支持轮询和随机算法切换
    * 新增 `GET /api/distribution/queue/algorithm-config` 获取算法配置接口
    * 新增 `POST /api/distribution/queue/algorithm-config` 更新算法配置接口
    * 全局配置变量 `QUEUE_ALGORITHM_CONFIG` 管理当前使用的算法
  - **🔄 全面应用**：
    * 更新所有队列排序相关函数，使用配置的算法替代硬编码的随机算法
    * 保护已分发队列的智能排序算法：使用配置的算法对待分发队列排序
    * 增量添加队列项：使用配置的算法对新增队列项排序
    * 队列生成和保存：使用配置的算法进行排序
  - **📊 算法特性**：
    * **公平性**：轮询算法确保每个成员获得分配机会的公平性
    * **可预测性**：分配结果可预测，便于业务规划和管理
    * **平衡性**：避免同一人连续分发，确保分配的平衡性
    * **兼容性**：保持现有API接口和数据结构不变
    * **可配置性**：支持在轮询和随机算法之间动态切换
  - **🔧 修改文件**：
    * `routers/distribution.py` - 核心算法实现和配置管理
    * `README.md` - 文档更新，包含新算法说明和配置指南
  - **✅ 验证结果**：
    * ✅ 轮询算法正确实现A→B→C→A循环分配
    * ✅ 时间优先级排序正常工作
    * ✅ 跨时间段轮询连续性保持
    * ✅ 配置切换功能正常，支持动态算法选择
    * ✅ 所有现有功能保持兼容，无破坏性变更
- **v2.3.4**: 修复队列更新中的人员替换逻辑错误 (2025-01-02)
  - **🚨 严重问题修复**：解决人员替换被错误处理为人员增加的严重逻辑错误
  - **🔍 问题描述**：
    * 场景：3个人员(A、B、C)各10条排班，将人员C替换为人员D
    * 错误结果：队列变成40条，同时包含A、B、C、D四个人员
    * 错误原因：程序将人员替换处理为人员增加，既保留了C又新增了D
    * 正确预期：队列应保持30条，只包含A、B、D三个人员
  - **✅ 修复方案**：
    * 新增 `detect_member_replacements` 函数：检测人员替换场景
    * 新增 `process_member_replacements` 函数：处理人员替换逻辑
    * 人员替换检测：识别删除旧人员+添加新人员且数量匹配的场景
    * 队列项更新：将被替换人员的队列项member字段更新为新人员
    * 保护已分发队列：已分发的队列项保持原成员不变，只更新待分发队列
  - **🎯 核心特性**：
    * **人员替换检测**：自动识别人员替换vs人员增加场景
    * **已分发队列保护**：已分发队列保持原成员不变
    * **待分发队列更新**：将待分发队列的成员更新为新人员
    * **数量一致性**：确保队列总数与排班规则预期数量一致
    * **兼容性保持**：与现有增量更新和保护性排序逻辑完全兼容
  - **🧪 验证结果**：
    * ✅ 正确检测人员替换场景（销售C -> 销售D）
    * ✅ 保护已分发队列：已分发的5条队列保持销售C不变
    * ✅ 更新待分发队列：待分发的5条队列更新为销售D
    * ✅ 避免数量错误：队列总数保持10条，不会变成20条
    * ✅ 不影响其他正常的增减量更新场景

- **v2.3.3**: 优化增量更新中新增队列项的排序算法 (2025-01-02)
  - **🎯 优化目标**：解决新增队列项排序不一致的问题，与生成队列时的算法保持一致
  - **🔍 问题分析**：
    * 新增队列项按输入顺序直接追加，没有进行随机排列
    * 可能导致人员聚集或时间段聚集问题
    * 与生成分发队列时使用的随机排序算法不一致
  - **✅ 优化方案**：
    * 在 `add_queue_items_incrementally` 函数中添加随机排序逻辑
    * 对新增队列项应用与 `sort_queue_with_random_algorithm` 相同的随机排序
    * 保持核心修复不变：已分发队列和现有待分发队列完全不变
    * 随机排序后仍使用连续的position分配（从max_position+1开始）
  - **🎯 核心特性**：
    * **保护性增量更新**：现有队列完全不变，只对新增项进行处理
    * **新增项随机排序**：避免人员聚集和时间段聚集问题
    * **算法一致性**：与生成队列时的随机排序逻辑完全一致
    * **Position连续性**：随机排序后仍保持position序号连续
  - **🧪 验证结果**：
    * ✅ 原有队列（包括已分发和待分发）完全保持不变
    * ✅ 新增队列项经过随机排序，有效避免聚集问题
    * ✅ Position序号分配正确，保持连续性（31-36）
    * ✅ 与生成队列时的随机排序效果一致

- **v2.3.2**: 彻底修复"更新分发队列"按钮的严重逻辑错误 (2025-01-02)
  - **🚨 严重问题修复**：解决队列更新功能违反"已分发队列不可变"核心原则的严重错误
  - **🔍 问题描述**：
    * 更新队列时，所有队列项（包括已分发队列）都被重新随机排序
    * 已分发队列的序号和位置被意外修改，导致数据混乱
    * 增量更新时仍然调用排序算法，破坏现有队列顺序
    * 违反了分布式队列的不可变原则
  - **✅ 彻底修复方案**：
    * **第一阶段**：新增 `sort_queue_with_distributed_protection` 保护性排序算法
    * **第二阶段**：新增 `add_queue_items_incrementally` 增量添加函数
    * **核心改进**：增量更新完全跳过排序步骤，直接追加新队列
    * 使用全局统一的position序号分配（不分队列类型）
    * 替换所有队列更新中的排序算法，确保一致性
  - **🎯 核心原则**：
    * **已分发队列不可变原则**：已分发队列保持原有position和所有属性
    * **增量零排序原则**：增量更新完全跳过排序，直接追加新队列
    * **全局position原则**：使用统一的position序号（不分免费/付费队列）
    * **减量限制原则**：减量更新只删除待分发队列
    * **序号连续性原则**：确保最终position序号连续(1,2,3...)
  - **🔧 修改的文件**：
    * `routers/distribution.py` - 新增保护性排序算法和增量添加函数
    * 删除 `routers/distribution copy.py` 备份文件避免混淆
  - **🧪 验证结果**：
    * ✅ 已分发队列在更新过程中完全保持不变
    * ✅ 增量更新（30条→36条）正确在末尾追加新队列，position为31-36
    * ✅ 原有队列顺序完全不变，无任何重新排序
    * ✅ Position序号使用全局统一分配，保持连续性
    * ✅ 减量更新只影响待分发队列，保护已分发队列

- **v2.3.1**: 修复队列顺序刷新页面后改变的问题 (2025-01-20)
  - **🐛 关键问题修复**：解决生成或更新分发队列后，刷新页面会导致队列序号改变的严重问题
  - **🔍 问题根源**：获取队列数据接口 `/api/distribution/queue/data` 每次调用都会重新执行随机排序并更新数据库
  - **✅ 修复方案**：
    * 获取队列数据时直接按position字段升序排序返回，不再重新排序
    * 只有在生成队列、更新队列、保存队列、重排序队列时才执行随机算法
    * 确保队列顺序的稳定性，刷新页面后顺序保持不变
  - **🎯 用户体验提升**：
    * 队列生成后顺序固定，不会因刷新页面而改变
    * 队列更新后顺序稳定，保持用户操作的一致性
    * 避免用户困惑和误操作，提升系统可靠性
  - **🔧 修改文件**：
    * `routers/distribution.py` - 修复获取队列数据接口逻辑
    * `README.md` - 更新文档说明和修复记录
  - **📊 影响范围**：获取队列数据接口 (`/api/distribution/queue/data`)
  - **⚡ 性能优化**：移除不必要的重复排序操作，提升接口响应速度
- **v2.3.0**: 重构分发队列排序算法为完全随机算法 (2025-01-20)
  - **🎯 核心问题修复**：解决原有"相邻约束交叉排序"算法导致的同一人连续分发问题
  - **🔧 算法重构**：将复杂的相邻约束算法替换为简单高效的完全随机排序算法
  - **⚡ 性能提升**：算法复杂度从 O(n²) 降低到 O(n)，显著提升处理速度
  - **🎯 分发机制优化**：
    * 分发系统从队列第一个开始按时间段检查
    * 符合时间段就分发，不符合就跳到下一个
    * 随机排序确保不会连续分发给同一个人
    * 消除时间段聚集造成的分发不均问题
  - **📊 影响范围**：
    * 生成分发队列接口 (`/api/distribution/queue/generate-and-save`)
    * 更新分发队列接口 (`/api/distribution/queue/update`)
    * 保存分发队列接口 (`/api/distribution/queue/save`)
    * 重排序队列接口 (`/api/distribution/queue/reorder`)
    * 获取队列数据接口 (`/api/distribution/queue/data`)
  - **✅ 功能特性**：
    * 完全随机性：每次生成的队列顺序都不相同
    * 分发公平性：每个人获得分发机会的概率相等
    * 消除偏向性：避免特定时间段或人员的偏向
    * 向后兼容：保持现有API接口和数据结构不变
    * 调试友好：输出详细的排序信息和示例数据
  - **🔧 修改文件**：
    * `routers/distribution.py` - 核心算法重构和所有相关接口更新
    * `README.md` - 文档更新，包含新算法说明和使用指南
  - **💡 用户体验提升**：彻底解决分发队列中同一人连续分发的问题，确保分发的公平性和随机性
- **v2.2.8**: 完善审批撤回通知推送接口 (2025-06-27)
  - **🆕 新功能**：完善审批撤回通知推送接口 `/api/websocket/shenpi_cehui`
  - **📋 功能描述**：向申请撤回线索的用户发送审批结果通知
  - **🔍 查询逻辑**：
    * 表连接：`ClueWithdraw.ID == SalaCrm.ID`, `ClueWithdraw.contact_person == User.name`
    * 查询条件：接收`lead_id`参数，查询指定线索的撤回申请记录
    * 查询字段：用户ID、姓名、分配日期、序号、撤回状态等
  - **📊 审批状态处理**：
    * 通过状态：`withdraw_status == 1` → "通过"
    * 拒绝状态：`withdraw_status == 2` → "拒绝"
    * 其他状态：显示为"未知状态(数值)"
  - **📨 消息模板**：
    * 格式：`你{日期}接收的第{序号}条线索的撤回申请已{状态}，点击前往查看`
    * 消息类型：`approval_notification`
    * 包含分配日期、线索序号、审批状态、时间戳等信息
  - **🔧 技术实现**：
    * 使用Query参数接收`lead_id`
    * 异步WebSocket消息推送
    * 完整的错误处理和日志记录
    * 在线状态检查，只向在线用户推送
    * 安全的日期字段处理
  - **🧪 测试工具**：
    * 新增测试接口 `/api/websocket/shenpi_cehui_test`
    * 支持数据库连接测试、表连接查询测试、指定线索查询测试
    * 提供详细的测试数据和WebSocket状态信息
  - **📋 修改文件**：`routers/msg_websocket.py` - 完善审批撤回通知推送接口
  - **✅ 预期效果**：用户在线索撤回申请审批完成后能够立即收到WebSocket推送通知
- **v2.2.7**: 修复客情跟进通知推送接口执行错误 (2025-06-27)
  - **🐛 问题修复**：修复客情跟进通知推送接口执行时的各种错误
  - **🔧 错误处理增强**：
    * 添加数据库查询异常处理，避免查询失败导致接口崩溃
    * 修复时间字段可能为None的问题，添加安全检查
    * 修复allocation_date字段可能为None导致的格式化错误
    * 添加完整的try-catch异常处理机制
  - **📊 查询优化**：
    * 分步执行数据库查询，先测试基本连接再执行复杂查询
    * 手动过滤24小时未跟进记录，避免数据库时间计算错误
    * 添加查询结果的详细日志记录
  - **🛡️ 安全性提升**：
    * 对所有可能为None的字段进行安全处理
    * 为成员和组长消息发送分别添加异常处理
    * 确保单个记录处理失败不影响其他记录
  - **🧪 测试工具**：
    * 新增测试接口 `/api/websocket/sala_crm_follow_tz_test`
    * 支持数据库连接测试、表连接查询测试、WebSocket状态测试
    * 提供详细的测试结果和错误诊断信息
  - **📋 修改文件**：`routers/msg_websocket.py` - 修复查询逻辑、异常处理和数据安全性
  - **✅ 修复效果**：接口现在能够稳定执行，正确处理各种边界情况和异常情况
- **v2.2.6**: 新增客情跟进通知推送接口 (2025-06-27)
  - **🆕 新功能**：客情跟进通知推送接口 `/api/websocket/sala_crm_follow_tz`
  - **📋 功能描述**：向销售成员和组长发送24小时未跟进线索的提醒通知
  - **🔍 查询逻辑**：
    * 表连接：`SalaCrm.ID == ClueSheet.ID`, `ClueSheet.contact_person == User.name`
    * 查询条件：未推送过通知、超过24小时未跟进、最后跟进时间在前两天内、销售部门用户
    * 查询字段：用户ID、姓名、身份、组别、分配日期、序号等
  - **📨 推送逻辑**：
    * 成员身份：向成员本人和对应组长发送通知
    * 非成员身份：只向用户本人发送通知
    * 成员模板：`你在{日期}接收到的第{序号}条线索在24小时内没有跟进记录，请注意及时跟进！`
    * 组长模板：`{成员}在{日期}接收到的第{序号}条线索在24小时内没有跟进记录，请协助及时跟进！`
  - **🔧 技术实现**：
    * 使用异步函数和WebSocket消息推送
    * 完整的错误处理和详细日志记录
    * 数据库事务一致性（推送成功后更新seeded字段）
    * 支持成员和组长双重通知机制
  - **📊 返回信息**：推送用户数、总线索数、在线用户数、更新记录数、详细推送结果
  - **📋 修改文件**：`routers/msg_websocket.py` - 新增完整的客情跟进通知推送接口
- **v2.2.5**: 修复WebSocket推送消息发送失败问题 (2025-06-27)
  - **🐛 问题修复**：修复`send_personal_message`方法执行失败的问题
  - **🔧 技术修复**：
    * 修复`send_personal_message`调用缺少`await`关键字的问题
    * 修复用户ID类型不匹配问题（数据库ID为整数，WebSocket连接ID为字符串）
    * 添加用户ID类型转换逻辑，确保类型一致性
  - **📊 调试增强**：
    * 在`send_personal_message`方法中添加详细的调试日志
    * 显示当前活跃连接列表和消息发送状态
    * 在推送逻辑中添加用户在线状态检查日志
    * 添加消息内容和发送结果的详细记录
  - **🧪 测试工具**：
    * 新增`/api/websocket/test-send`测试接口
    * 支持手动测试向指定用户发送WebSocket消息
    * 提供详细的测试结果和在线状态信息
  - **📋 修改文件**：
    * `routers/msg_websocket.py` - 修复异步调用、类型转换和调试日志
  - **✅ 修复效果**：WebSocket消息现在能够正确发送给在线用户，推送功能完全正常
- **v2.2.4**: 重构WebSocket推送为HTTP接口调用方式 (2025-06-27)
  - **🔗 自动推送集成**：线索分发接口(`/api/distribution/media/auto-dispatch`)成功分发线索后自动调用WebSocket推送
  - **🚀 实时通知**：用户无需手动触发推送，分发成功后立即收到线索通知
  - **🛡️ 错误隔离**：WebSocket推送失败不影响主要的分发流程，确保系统稳定性
  - **📊 详细日志**：记录推送调用的成功/失败状态，包括推送用户数和线索数量
  - **🏗️ 架构优化**：
    * 改为使用HTTP客户端调用`/api/websocket/xiansuo`接口，避免直接函数调用
    * 保持模块间的松耦合，避免循环导入等问题
    * 启用`/api/websocket/xiansuo`接口，移除注释状态
  - **🔧 技术实现**：
    * 使用`httpx.AsyncClient`发送HTTP GET请求到推送接口
    * 在分发成功后（`successful_count > 0`时）自动调用推送接口
    * 设置15秒超时时间，避免长时间等待
    * 使用try-catch包装推送调用，确保异常不影响主流程
    * 分别处理超时异常、网络异常和其他异常
    * 添加详细的日志记录，包括HTTP状态码和响应内容
  - **🌐 网络配置**：
    * 默认调用本地接口：`http://127.0.0.1:8000/api/websocket/xiansuo`
    * 可根据实际部署环境调整base_url配置
    * 支持详细的网络错误诊断和日志记录
  - **📋 修改文件**：
    * `routers/distribution.py` - 移除直接函数导入，添加HTTP客户端调用逻辑
    * `routers/msg_websocket.py` - 启用`/api/websocket/xiansuo`接口
  - **✅ 预期效果**：用户在线索分发成功后能够立即收到WebSocket推送通知，提升实时性和用户体验，同时保持系统架构的清晰性
- **v2.2.0**: 修复队列更新跨渠道数据干扰bug (2025-06-25)
  - **🐛 问题描述**：用户点击"更新分发队列"按钮后，虽然选择更新的是"新媒体免费渠道"，该渠道的队列确实正确更新了，但是其他渠道（如"电商渠道"）的队列数据被意外清空
  - **🔍 根本原因**：前端队列更新逻辑同时更新两个渠道，无论用户选择哪个渠道，都会并行调用两个渠道的更新API
  - **✅ 修复方案**：
    * **前端架构重构**：移除同时更新多个渠道的逻辑，改为只更新当前选中的渠道
    * **后端变更检测**：新增 `detect_queue_changes()` 函数，检测队列是否真的需要更新
    * **渠道隔离验证**：在所有数据库查询中强化渠道类型过滤，确保完全隔离
    * **数据一致性检查**：新增 `validate_channel_isolation()` 函数验证渠道间数据完整性
  - **🔧 技术实现**：
    * 前端：`updateQueue()` 函数只更新 `queueForm.value.channelType` 指定的渠道
    * 后端：新增 `/api/distribution/queue/changes` 和 `/api/distribution/queue/validation` API
    * 数据库：所有队列操作都明确添加 `channel_type` 过滤条件
    * 日志：所有操作日志都添加渠道标识，便于问题追踪
  - **🎯 修复效果**：
    * ✅ 更新单个渠道不再影响其他渠道数据
    * ✅ 支持变更检测，避免不必要的更新操作
    * ✅ 完全的渠道间数据隔离
    * ✅ 详细的操作日志和错误追踪
    * ✅ 数据一致性验证机制
  - **📋 新增API**：
    * `GET /api/distribution/queue/changes` - 检测队列变更
    * `GET /api/distribution/queue/validation` - 验证队列完整性
  - **⚠️ 重要提醒**：此修复确保了渠道间的完全隔离，是系统数据安全的重要保障
- **v1.2.9**: 修复队列更新逻辑错误：队列更新时按总量而非增量计算的问题
  - **问题修复**：修复队列更新时按成员总量而非按成员+班次增量计算的逻辑错误
  - **具体场景**：2个人员，每人白班+晚班各2条队列（共8条），修改白班为3条时，错误逻辑会增加6条而非预期的2条
  - **根本原因**：原逻辑按成员统计已分发数量，未按班次（时间段）分别统计，导致计算错误
  - **修复方案**：
    * 改为按 `成员+时间段` 的组合键统计已分发数量
    * 确保每个成员的每个班次独立计算增量
    * 修复后正确按增量计算：2人 × 1条白班增量 = 2条新增队列
  - **技术细节**：
    * 统计键从 `member` 改为 `member_time_slot` (如: "成员A_09:00-18:00")
    * 每个规则独立计算：新规则量 - 该成员该时间段已分发量 = 需新增量
    * 保持已分发队列完全不变的核心原则
- **v1.2.8**: 修复队列更新后排序显示不一致问题，重构队列更新架构
  - **问题修复**：解决队列更新后显示按时间段顺序排列，刷新页面后变回交叉排列的不一致问题
  - **架构重构**：移除前端复杂的队列更新逻辑，将所有队列更新逻辑迁移到后端
  - **新增API**：`PUT /api/distribution/queue/update` - 智能队列更新接口
  - **队列更新逻辑**：
    * 保持已分发队列不变：所有状态为"已分发"的队列记录保持原有序号和分配关系
    * 队列增加场景：在原有队列基础上新增队列项，使用交叉排列算法分配
    * 队列减少场景：不减少已分发队列，仅为需要增加的成员分配新的待分发队列
    * 立即应用时间段交叉排序并返回结果
  - **前端简化**：前端只负责接收和显示后端返回的数据，确保前后端数据一致性
  - **技术优势**：避免前后端数据同步问题，确保更新后立即显示正确的交叉排列
  - **🔧 重要修复**：修复队列更新按总量而非增量计算的逻辑错误
    * **问题**：队列更新时按新规则总量与已分发量比较，忽略了待分发队列，导致队列数量计算错误
    * **修复**：改为按新规则总量与现有队列总数（已分发+待分发）比较，实现真正的增量更新
    * **场景验证**：2人×2班次×2条=8条队列，白班改为3条时，正确增加2条而非6条
    * **算法优化**：新增队列减少场景的智能处理，删除多余的待分发队列项
- **v1.2.7**: 实现完整的班次分组排序逻辑，统一所有队列接口的排序规则
  - 新增 `sort_queue_by_shift_groups` 函数，实现按班次分组的完整排序逻辑
  - 修改 `GET /api/distribution/queue/data` 接口，按班次分组逻辑返回数据并更新position
  - 修改 `POST /api/distribution/queue/save` 接口，保存后按班次分组逻辑重新排序
  - 修改 `POST /api/distribution/queue/reorder` 接口，使用班次分组逻辑重新排序
  - 排序规则：按shift班次分组→班次分组按最早时间排序→分组内交叉生成→position从1开始
  - 确保所有队列相关接口都使用统一的班次分组排序逻辑
- **v1.2.6**: 优化队列排序逻辑，实现按时间和ID的精确排序（已被v1.2.7替代）
  - 修改 `POST /api/distribution/queue/save` 接口，保存后自动按时间顺序重新排序
  - 新增 `POST /api/distribution/queue/reorder` 接口，支持手动重新排序现有队列
  - 排序规则：按time_slot_beg时间早的在前，相同时间的按ID小的在前，position从1开始
  - 修改 `GET /api/distribution/queue/data` 接口，返回数据按时间和ID排序
  - 确保队列数据在保存和获取时都保持正确的时间顺序
- **v1.2.1**: 优化分发队列生成的时间排序逻辑
  - 优化 `POST /api/distribution/queue/save` 接口的队列生成逻辑
  - 修改队列排序算法，现在优先处理 `time_range_start`（时间段开始时间）较早的规则
  - 应用于交替配额轮循法和动态权重轮循法两种分发算法
  - 确保早班人员优先分配，提高分发队列的时间合理性
  - 更新函数注释和API文档，添加详细的队列保存接口说明
- **v1.2.0**: 优化分发规则编辑功能
  - 优化 `PUT /api/distribution/rules/{rule_id}` 接口的时间范围处理逻辑
  - 修复跨日期编辑排班时时间戳不正确的问题
  - `time_range_start` 和 `time_range_end` 现在基于排班对应的日期（`DistributionRule.date`）生成
  - 解决了在今天修改未来日期排班时的时间戳错误问题
  - 确保编辑未来日期排班时生成正确的完整时间戳
  - 更新相关文档和API说明
- **v1.1.0**: 新增自动化任务系统
  - 新增自动化任务调度模块 (`routers/rule_task.py`)
  - 实现基于 BackgroundTasks + time.sleep 的定时任务调度
  - 支持动态修改任务启动时间并重新调度
  - 提供任务开关控制，可实时启停定时任务
  - 完全集成到分发计划页面，状态实时同步
  - 支持手动触发队列生成（测试功能）
  - 完善任务恢复机制，应用重启后自动恢复活跃任务
  - 更新数据库模型，新增 `DistributionRulesTask` 表
- **v1.0.1**: 修复分发规则页面API密钥验证问题
  - 修复 `distribution_rules.html` 页面API请求500错误
  - 添加自动API密钥获取和设置机制
  - 完善全局错误处理和调试信息
  - 优化页面初始化流程，确保API密钥在请求前设置完成
- **v1.0.0**: 初始版本，基础功能实现
  - 支持多渠道线索管理
  - 实现智能分发系统
  - 完善权限管理体系

## 📡 WebSocket线索推送API

### WebSocket连接端点

#### WS /api/websocket/{user_id} (路径参数方式)

**功能描述**
建立WebSocket连接，用于实时接收线索推送通知。

**连接参数**
- `user_id`: 用户ID（路径参数）

**连接示例**
```javascript
const websocket = new WebSocket('ws://localhost:8000/api/websocket/your_user_id');

#### WS /api/websocket?token={user_id} (查询参数方式)

**功能描述**
建立WebSocket连接，兼容前端查询参数方式。

**连接参数**
- `token`: 用户ID（查询参数）

**连接示例**
```javascript
const websocket = new WebSocket('ws://localhost:8000/api/websocket?token=your_user_id');

#### WS /api/test-websocket (测试端点)

**功能描述**
简单的WebSocket测试端点，不需要用户ID参数。

**连接示例**
```javascript
const websocket = new WebSocket('ws://localhost:8000/api/test-websocket');

websocket.onmessage = function(event) {
    const data = JSON.parse(event.data);
    if (data.type === 'clue_notification') {
        console.log(`收到线索通知: ${data.message}`);
    }
};
```

**消息格式**
```json
{
    "type": "clue_notification",
    "message": "你收到了3条线索，请前往查看",
    "clue_count": 3,
    "timestamp": "2025-06-26T10:30:00",
    "user_name": "张三"
}
```

### GET /api/login_websocket

**功能描述**
获取WebSocket登录信息，返回用户信息和WebSocket连接URL。

**请求头**
```
Authorization: Bearer user_your_user_id
```

**响应示例**
```json
{
    "success": true,
    "message": "登录成功",
    "user_id": "your_user_id",
    "user_name": "张三",
    "websocket_url": "/api/websocket/your_user_id"
}
```

### GET /api/xiansuo_websocket

**功能描述**
执行线索推送，向有新线索的在线用户发送WebSocket通知。

**响应示例**
```json
{
    "success": true,
    "message": "线索推送完成，成功推送给 2 个在线用户",
    "pushed_users": 2,
    "total_users": 5,
    "total_clues": 8,
    "online_users": 3,
    "push_details": [
        {
            "user_id": "user123",
            "user_name": "张三",
            "clue_count": 3,
            "status": "success"
        },
        {
            "user_id": "user456",
            "user_name": "李四",
            "clue_count": 5,
            "status": "success"
        }
    ]
}
```

### GET /api/websocket/status

**功能描述**
获取WebSocket连接状态信息。

**响应示例**
```json
{
    "success": true,
    "online_users_count": 3,
    "online_users": ["user123", "user456", "user789"]
}
```

### GET /api/websocket/debug

**功能描述**
调试线索查询接口，用于排查数据库查询问题和监控数据状态。

**响应示例**
```json
{
    "success": true,
    "debug_info": {
        "current_date": "2025-06-27",
        "total_queues_today": 20,
        "distributed_queues_today": 4,
        "pending_push_queues_today": 0,
        "pending_members": 0,
        "member_details": [],
        "join_query_success": true,
        "join_query_result": 0,
        "online_users": ["13202506090001"]
    }
}
```

**字段说明**
- `total_queues_today`: 今日总队列数
- `distributed_queues_today`: 今日已分发队列数
- `pending_push_queues_today`: 今日待推送队列数
- `pending_members`: 待推送成员数量
- `member_details`: 成员详细信息
- `join_query_success`: JOIN查询是否成功
- `join_query_result`: JOIN查询结果数量
- `online_users`: 当前在线用户列表

### GET /api/websocket/test-message

**功能描述**
测试新的模板消息格式，验证排班数据统计功能。

**请求参数**
- `user_name` (可选): 用户姓名，默认"肖月丰"
- `clue_count` (可选): 线索数量，默认3

**响应示例**
```json
{
    "success": true,
    "user_name": "肖月丰",
    "clue_count": 3,
    "distribution_rules_count": 1,
    "M_expected_total": 27,
    "X_actual_total": 10,
    "Y_remaining": 17,
    "message": "你收到了3条线索，今日预计接收27条，当前已接收10条，剩余17条，请前往查看",
    "distribution_rules": [
        {
            "id": 1725,
            "channel_type": "新媒体渠道",
            "group_name": "A-1组",
            "expected_total": 2,
            "expected_free": 20,
            "expected_paid": 5,
            "actual_total": 5,
            "actual_free": 4,
            "actual_paid": 1
        }
    ]
}
```

### 测试页面

访问 `/websocket-test` 可以打开WebSocket功能测试页面，用于测试连接和消息推送功能。

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交变更
4. 发起 Pull Request

## 📄 许可证

本项目采用私有许可证，仅供内部使用。

## 📞 联系方式

如有问题或建议，请联系项目维护团队。 

## 🔄 分发队列时间分组排序机制

### 🧩 基本信息

- **技术栈：** Python + FastAPI
- **接口地址：** `http://0.0.0.0:8000/api/distribution/queue/save`
- **请求方式：** `POST`
- **核心函数：** `sort_queue_by_shift_groups()`

### 📌 请求体字段说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `queue_date` | string | 是 | 队列日期，格式为 `"YYYY-MM-DD"` |
| `channel_type` | string | 是 | 渠道类型，如"新媒体渠道"、"电商渠道" |
| `free_queue` | array | 是 | 免费队列数据列表，包含多个队列对象 |
| `paid_queue` | array | 是 | 付费队列数据列表，包含多个队列对象 |

### 📝 队列对象数据结构

```json
{
  "position": 1,
  "group_name": "B组",
  "leader": "万彬",
  "member": "陈宇波",
  "time_slot": "09:00-18:30",
  "status": "pending"
}
```

### 🔍 核心处理逻辑

系统对传入的 `free_queue` 和 `paid_queue` 数组进行以下处理：

#### 🆕 基于时间段的轮询分发算法 (v2.5.0)

**核心设计原则：**
1. **时间优先级**：按排班开始时间（time_slot_beg）升序排序所有可用人员
2. **轮询分配**：执行甲→丁→乙→丙为一轮完整循环，重复轮询直到所有数据分配完毕
3. **跨渠道隔离**：维持跨渠道隔离（新媒体付费/免费、电商渠道独立处理）
4. **交叉排列约束**：确保相邻队列不能有相同时间段或成员名
5. **分布式队列保护**：保留现有的队列更新逻辑（分布式队列不可变原则）
6. **公平性保障**：确保每个成员获得分配机会的公平性和可预测性

**算法流程详解：**

#### 1️⃣ 时间段信息提取
- 从每个队列项的 `time_slot` 字段提取起始时间（格式如 "09:00-18:30"）
- 转换为时间对象用于排序比较
- 异常处理：解析失败时使用默认时间 "09:00"

#### 2️⃣ 时间段和成员排序
- 按时间段开始时间升序排序
- 相同时间段内按成员名排序确保一致性
- 示例：甲（09:00）→ 丁（09:30）→ 乙（13:30）→ 丙（18:30）

#### 3️⃣ 全局轮询顺序创建
- 按时间段分组，获取每个时间段的唯一成员列表
- 创建全局轮询顺序：按时间段开始时间排序的所有唯一成员
- 确保轮询顺序的时间优先级和成员一致性

#### 4️⃣ 成员队列项分组
- 按成员分组所有队列项，使用 (时间段, 成员) 作为分组键
- 每个成员内按ID排序，确保成员内顺序稳定
- 支持同一成员在不同时间段的独立处理

#### 5️⃣ 轮询分配执行
- 从每个成员中依次取出一个元素进行轮询分配
- 执行 A→B→C→D→A→B→C→D... 的循环分配
- 确保公平分配：每轮循环中每个成员都有机会被分配

#### 🆕 完全随机排序算法 (v2.3.0)

**核心设计原则：**
1. **完全随机性**：将队列项完全随机打乱，消除任何规律性排列
2. **避免连续分发**：解决同一人连续分发的问题
3. **消除时间段聚集**：避免同一时间段人员聚集在一起
4. **分发公平性**：确保每个人获得分发机会的公平性
5. **顺序稳定性**：排序完成后队列顺序固定，不会因刷新页面而改变

**时间段轮询算法应用时机：**
- ✅ 生成分发队列时（`/api/distribution/queue/generate-and-save`）- 默认使用时间段轮询算法
- ✅ 更新分发队列时（`/api/distribution/queue/update`）- 根据配置使用相应算法
- ✅ 保存分发队列时（`/api/distribution/queue/save`）- 根据配置使用相应算法
- ✅ 重排序队列时（`/api/distribution/queue/reorder`）- 根据配置使用相应算法
- ❌ 获取队列数据时（`/api/distribution/queue/data`）- 直接按position排序返回

**算法配置管理：**
- **默认算法**：`time_slot_polling` - 基于时间段的轮询分发算法
- **备选算法**：`polling` - 传统轮询算法，`random` - 随机算法
- **配置接口**：`GET/POST /api/distribution/queue/algorithm-config` - 查看和切换算法
- **动态切换**：支持在不同算法之间动态切换，无需重启服务

**随机算法应用时机：**
- ✅ 作为备选算法，可通过配置接口切换使用
- ✅ 保持向后兼容，现有使用随机算法的场景可继续使用

**算法流程：**

#### 1️⃣ 队列项随机打乱

- 创建原队列的副本，避免修改原列表
- 使用 `random.shuffle()` 进行完全随机打乱
- 确保每次生成的队列顺序都不相同

#### 2️⃣ 分发机制适配

- 分发系统从队列第一个开始检查时间段
- 如果时间段符合当前时间就分发
- 不符合就跳到下一个，直到找到符合的
- 随机排序确保不会连续遇到同一个人

#### 3️⃣ 公平性保障

- 每个队列项在任何位置出现的概率相等
- 避免特定时间段或特定人员的偏向性
- 消除人为或算法造成的分发不均问题

#### 4️⃣ 性能优化

- 算法复杂度为 O(n)，性能优秀
- 无需复杂的约束检查和计算
- 快速生成随机排序结果

#### 5️⃣ 调试信息输出

- 显示随机排序前的队列总数
- 输出前10项排序结果作为示例
- 包含成员姓名、组别、时间段等关键信息

#### 6️⃣ 返回随机排序队列

- 将完全随机排序后的队列返回
- 保持原有的数据结构和字段
- 确保与现有分发系统完全兼容

### 🔄 排序结果示例

**输入数据（实际业务场景）：**
```json
{
  "queue_date": "2025-06-19",
  "channel_type": "新媒体渠道",
  "free_queue": [
    {
      "position": 1,
      "group_name": "B组",
      "member": "陈宇波",
      "time_slot": "09:00-18:30",
      "status": "pending"
    },
    {
      "position": 2,
      "group_name": "B组",
      "member": "赵仲磊",
      "time_slot": "09:00-23:59",
      "status": "pending"
    },
    {
      "position": 3,
      "group_name": "C组",
      "member": "李研研",
      "time_slot": "09:00-18:30",
      "status": "pending"
    },
    {
      "position": 4,
      "group_name": "C组",
      "member": "江林丽",
      "time_slot": "09:00-23:59",
      "status": "pending"
    },
    {
      "position": 26,
      "group_name": "B组",
      "member": "赵仲磊",
      "time_slot": "13:30-23:59",
      "status": "pending"
    },
    {
      "position": 27,
      "group_name": "B组",
      "member": "宁紫微",
      "time_slot": "18:30-23:59",
      "status": "pending"
    },
    {
      "position": 36,
      "group_name": "B组",
      "member": "赵仲磊",
      "time_slot": "18:30-23:59",
      "status": "pending"
    }
  ]
}
```

**处理过程（多时间段复杂场景）：**
```
1. 提取时间: [09:00, 09:00, 09:00, 09:00, 13:30, 18:30, 18:30]
2. 时间排序: 按起始时间升序排列
3. 时间分组: 
   - 09:00组: [B组-陈宇波, B组-赵仲磊, C组-李研研, C组-江林丽] (4项)
   - 13:30组: [B组-赵仲磊] (1项)
   - 18:30组: [B组-宁紫微, B组-赵仲磊] (2项)
4. 组内轮询分配:
   - 09:00组: B组、C组轮询 → B组-陈宇波, C组-李研研, B组-赵仲磊, C组-江林丽
   - 13:30组: 仅B组 → B组-赵仲磊
   - 18:30组: 仅B组 → B组-宁紫微, B组-赵仲磊
```

**输出结果（时间优先 + 组内轮询）：**
```json
{
  "free_queue": [
    {
      "position": 1,
      "group_name": "B组",
      "member": "陈宇波",
      "time_slot": "09:00-18:30"
    },
    {
      "position": 2,
      "group_name": "C组",
      "member": "李研研",
      "time_slot": "09:00-18:30"
    },
    {
      "position": 3,
      "group_name": "B组",
      "member": "赵仲磊",
      "time_slot": "09:00-23:59"
    },
    {
      "position": 4,
      "group_name": "C组",
      "member": "江林丽",
      "time_slot": "09:00-23:59"
    },
    {
      "position": 5,
      "group_name": "B组",
      "member": "赵仲磊",
      "time_slot": "13:30-23:59"
    },
    {
      "position": 6,
      "group_name": "B组",
      "member": "宁紫微",
      "time_slot": "18:30-23:59"
    },
    {
      "position": 7,
      "group_name": "B组",
      "member": "赵仲磊",
      "time_slot": "18:30-23:59"
    }
  ]
}
```

### 📊 排序效果分析

#### 时间优先排序效果
- **09:00时间段**：位置1-4，早班成员优先获得分发机会
- **13:30时间段**：位置5，午晚班成员其次
- **18:30时间段**：位置6-7，晚班成员最后

#### 组内轮询公平性
- **09:00组**：B组和C组交替分配，确保各组公平
- **13:30组**：仅B组，直接分配
- **18:30组**：仅B组，按成员顺序分配

#### 业务价值体现
1. **工作效率提升**：早班成员优先处理线索，提高响应速度
2. **资源合理配置**：不同时间段成员按工作时间合理分配
3. **公平性保障**：同时间段内各组成员轮询分配，避免偏向性
4. **可预测性增强**：排序规则明确，便于业务规划和管理

### 🛠️ 相关API接口

| 接口路径 | 方法 | 功能描述 |
|---------|------|---------|
| `/api/distribution/queue/save` | POST | 保存分发队列数据（自动应用时间分组排序） |
| `/api/distribution/queue/update` | PUT | 智能更新分发队列（保持已分发队列不变，应用交叉排序） |
| `/api/distribution/queue/reorder` | POST | 重新排序现有分发队列 |
| `/api/distribution/queue/data` | GET | 获取已保存的分发队列数据（按时间分组排序） |
| `/api/distribution/media/auto-dispatch` | POST | 线索自动分发（使用相同排序逻辑） |

### ⚠️ 注意事项

1. **字段依赖**: 依赖队列项的 `time_slot` 字段，格式必须为 `"HH:mm-HH:mm"`
2. **时间格式**: 时间格式必须为24小时制，如 `"09:00"`, `"13:30"`, `"18:30"`
3. **默认时间**: 如果 `time_slot` 字段缺失或格式错误，系统使用默认时间 `"09:00"`
4. **组内排序**: 同一时间组内按组名字母顺序排序，确保分配的一致性
5. **轮询公平**: 采用轮询算法确保同一时间组内各组成员的公平分配
6. **异常处理**: 系统具备完善的错误处理机制，确保在数据异常时的稳定运行

### 📊 算法特性

- **时间复杂度**: O(n log n) - 主要消耗在初始排序
- **空间复杂度**: O(n) - 需要额外存储分组信息
- **稳定性**: 稳定排序，相同时间的队列项保持相对顺序
- **公平性**: 轮询算法确保各组成员的公平分配

## 📝 版本更新记录

### v2.1.1 - 队列生成架构重构 (2025-06-25)

#### 🎯 核心修复
- **前后端职责分离**：完全移除前端队列生成逻辑，所有队列处理统一在后端完成
- **立即应用排序**：修复了点击"生成分发队列"后需要刷新页面才能看到交叉排序的问题
- **新增专用API**：创建 `/api/distribution/queue/generate-and-save` 接口，专门处理队列生成和排序

#### 🔧 技术实现
- **移除前端逻辑**：删除了前端的 `generateQueueData` 函数和相关轮询算法
- **后端完整处理**：新API包含完整的队列生成、时间段交叉排序和数据保存流程
- **即时排序应用**：队列生成后立即应用时间段交叉排序，无需额外步骤

#### 📊 架构优化
**修复前的问题**：
```
前端生成队列 → 发送到后端保存 → 后端应用排序 → 前端显示原始数据 → 需要刷新页面
```

**修复后的流程**：
```
前端调用API → 后端生成队列 → 后端应用排序 → 返回已排序数据 → 前端直接显示
```

#### 🎯 解决的问题
1. **立即生效**：点击"生成分发队列"后立即看到时间段交叉排序效果
2. **架构清晰**：前端专注展示，后端负责所有业务逻辑
3. **数据一致性**：避免前后端排序逻辑不一致的问题
4. **用户体验**：无需刷新页面即可看到正确的队列排序

#### 🔄 影响的功能模块
- **新增API**：`/api/distribution/queue/generate-and-save`
- **前端重构**：`generateQueue` 函数改为调用后端API
- **移除代码**：删除前端的队列生成和排序逻辑
- **保持兼容**：现有的队列查看和管理功能不受影响

#### ⚡ 性能提升
- **减少网络传输**：避免前端生成大量数据后传输到后端
- **统一处理**：所有队列操作在后端完成，提高处理效率
- **内存优化**：前端不再需要存储和处理复杂的队列生成逻辑

---

### v2.2.0 - 相邻约束交叉排序算法 (2025-06-25)

#### 🎯 核心改进
- **相邻约束交叉排序算法**：实现了全新的相邻约束检查机制，确保队列分发的合理性和多样性
- **智能选择算法**：基于优先级的候选项选择，最大化约束满足率
- **降级策略**：当无法满足约束时，提供合理的降级选择机制

#### 🔧 技术实现
- **约束检查**：实现相邻队列的时间段和成员名称约束检查
- **优先级算法**：四级优先级选择机制，确保最优的队列排列
- **统计分析**：实时统计约束满足情况，提供详细的性能指标

#### 📊 约束条件
**核心约束：**
1. **时间段约束**：相邻队列的 `time_slot_beg` 不能相同
2. **成员约束**：相邻队列的 `member_name` 不能相同
3. **降级策略**：只有在没有可供交叉的数据时才允许违反约束

**优先级策略：**
- **优先级1**：时间段不同 + 成员不同（完全满足约束）
- **优先级2**：时间段不同 + 成员相同（部分满足约束）
- **优先级3**：时间段相同 + 成员不同（部分满足约束）
- **优先级4**：时间段相同 + 成员相同（降级策略）

#### 🎯 适用范围
- **新媒体付费渠道队列生成**
- **新媒体免费渠道队列生成**
- **电商渠道队列生成**
- **队列更新时的新增部分**

#### 📈 性能优化
- **算法复杂度**：保持 O(n²) 时间复杂度，适用于中等规模数据
- **内存使用**：优化了候选项选择，减少内存占用
- **约束满足率**：显著提高了队列排列的合理性

#### 🧪 测试验证
- **约束检查**：验证了相邻约束的正确性
- **优先级选择**：确认了四级优先级算法的有效性
- **统计功能**：验证了约束满足率统计的准确性

#### 📋 兼容性说明
- **向后兼容**：现有的API接口保持不变，无需修改前端代码
- **数据格式**：队列数据结构保持一致，无需数据迁移
- **配置要求**：无需额外的配置变更
- **跨渠道隔离**：保持现有的跨渠道隔离机制

#### 🔄 影响的功能模块
- **队列生成**：所有渠道的队列生成都应用新算法
- **队列更新**：队列更新时的新增部分使用新算法
- **队列重排序**：手动重排序功能使用新算法
- **统计报告**：新增约束满足率统计信息

---

### v2.1.0 - 时间段交叉排序优化 (2025-06-25)

#### 🎯 核心改进
- **时间段交叉排序算法**：实现了全新的时间段交叉排序机制，解决了队列分发中的时间段阻塞问题

#### 🔧 技术实现
- **算法优化**：将原有的时间段顺序排序改为时间段交叉轮换排序
- **并行处理支持**：不同时间段的队列可以独立运行，避免后续时间段等待前面时间段完成
- **自动跳过机制**：分发系统会自动跳过不符合当前时间段的队列项

#### 📊 排序逻辑变更
**变更前**：
```
时间段顺序：09:00-18:30 → 09:00-18:30 → ... → 13:30-23:59 → 13:30-23:59 → ... → 18:30-23:59 → 18:30-23:59
问题：后续时间段需要等待前面时间段完全处理完毕
```

**变更后**：
```
时间段交叉：09:00-18:30 → 13:30-23:59 → 18:30-23:59 → 09:00-18:30 → 13:30-23:59 → 18:30-23:59 → ...
优势：不同时间段并行处理，无阻塞问题
```

#### 🎯 业务价值
1. **消除时间段阻塞**：13:30时间段不再需要等待9:00时间段完成
2. **提高分发效率**：多个时间段可以同时进行分发处理
3. **增强系统响应性**：分发系统可以根据当前时间自动选择合适的队列项
4. **保持公平性**：在交叉排序的基础上，仍然保持组内轮询的公平分配

#### 🔄 影响的功能模块
- **队列生成算法**：`sort_queue_by_shift_groups` 函数
- **队列保存接口**：`/api/distribution/queue/save`
- **队列重排接口**：`/api/distribution/queue/reorder`
- **队列数据获取**：`/api/distribution/queue/data`
- **自动分发系统**：`/api/distribution/media/auto-dispatch`

#### ⚡ 性能优化
- **算法复杂度**：保持 O(n log n) 时间复杂度
- **内存使用**：优化了分组存储，减少内存占用
- **处理速度**：交叉排序算法提高了大数据量下的处理效率

#### 🧪 测试验证
- **单元测试**：创建并通过了时间段交叉排序的完整测试用例
- **功能验证**：验证了3个时间段（9:00-18:30, 13:30-23:59, 18:30-23:59）的交叉排序效果
- **性能测试**：确认了算法在大数据量下的稳定性和效率

#### 📋 兼容性说明
- **向后兼容**：现有的API接口保持不变，无需修改前端代码
- **数据格式**：队列数据结构保持一致，无需数据迁移
- **配置要求**：无需额外的配置变更

### 🔧 电商渠道队列生成修复 (2025-06-25)

#### 🐛 问题描述
修复了电商渠道队列生成功能的关键问题：
- **问题现象**：点击"生成分队列"按钮后，只生成新媒体渠道队列，电商渠道队列未生成
- **根本原因**：电商渠道的排班规则中 `expected_free` 和 `expected_paid` 字段值为0，导致队列生成逻辑跳过电商渠道

#### ✅ 修复方案
1. **队列生成逻辑优化**：
   - 为电商渠道添加特殊处理逻辑
   - 当 `expected_free` 为0时，自动使用 `expected_total` 字段值
   - 保持新媒体渠道原有逻辑不变

2. **修改的文件**：
   - `routers/distribution.py` - 主要队列生成API
   - `routers/rule_task.py` - 同步队列生成函数

3. **核心修复代码**：
   ```python
   # 电商渠道特殊处理：如果expected_free为0，则使用expected_total
   if request.channel_type == "电商渠道":
       group_expected = sum(
           rule.expected_free if rule.expected_free > 0 else rule.expected_total
           for rule in group_rules
       )
   ```

#### 🧪 测试验证
- **测试结果**：电商渠道成功生成20项队列（沈悄、熊欢欢各10项）
- **新媒体渠道**：正常生成100项队列（免费50项，付费50项）
- **时间段交叉排序**：两个渠道都正确应用了时间段交叉排序算法

#### 📊 修复效果
- ✅ 电商渠道队列正常生成
- ✅ 新媒体渠道功能保持不变
- ✅ 时间段交叉排序正常工作
- ✅ 向后兼容，无需前端修改

### 🔧 新媒体渠道队列生成修复 (2025-06-25)

#### 🐛 问题描述
在修复电商渠道队列生成后，发现新媒体渠道队列无法生成：
- **问题现象**：电商渠道队列正常生成，但新媒体渠道出现500内部服务器错误
- **根本原因**：修复电商渠道时，错误地修改了新媒体渠道的付费队列生成逻辑

#### ✅ 修复方案
1. **问题分析**：
   - 新媒体渠道数据结构：A-1组(免费50项)，A-2组(付费50项)
   - 错误修改：将新媒体渠道付费队列逻辑改为 `if rule.expected_paid > 0`
   - 实际情况：A-2组的 `expected_paid` 可能为0，但仍需生成付费队列

2. **修复逻辑**：
   ```python
   # 新媒体渠道：使用原始逻辑，不过滤expected_paid > 0
   group_expected = sum(rule.expected_paid for rule in group_rules)
   ```

3. **修改文件**：
   - `routers/distribution.py` - 恢复新媒体渠道原始逻辑
   - `routers/rule_task.py` - 同步修复

#### 🧪 测试验证
- **电商渠道**：20项队列（沈悄、熊欢欢各10项）✅
- **新媒体渠道**：100项队列（免费50项，付费50项）✅
- **时间段交叉排序**：两个渠道都正确应用 ✅

#### 📊 最终效果
- ✅ 电商渠道和新媒体渠道都能正常生成队列
- ✅ 保持各渠道原有的数据结构和逻辑
- ✅ 时间段交叉排序算法正常工作
- ✅ 完全向后兼容，无需前端修改

### 🚀 队列生成用户体验优化 (2025-06-25)

#### 🎯 优化目标
解决队列生成功能的用户体验问题：
- **问题1**：新媒体渠道显示"生成失败"但实际队列已生成的误导性提示
- **问题2**：页面刷新时两个渠道队列加载顺序不一致，存在明显延迟差异
- **问题3**：缺乏清晰的加载状态提示和进度反馈

#### ✅ 优化方案

1. **并行队列生成**：
   ```javascript
   // 原来：顺序处理，总时间 = 渠道1时间 + 渠道2时间
   for (const channelType of channelTypes) {
       await generateForChannel(channelType);
   }

   // 优化后：并行处理，总时间 = max(渠道1时间, 渠道2时间)
   const generatePromises = channelTypes.map(channelType =>
       generateForChannel(channelType)
   );
   const results = await Promise.all(generatePromises);
   ```

2. **智能错误处理**：
   - 区分真正的失败和"队列已存在"的情况
   - 提供详细的成功/失败状态反馈
   - 避免误导性的错误提示

3. **并行数据加载**：
   ```javascript
   // 原来：顺序加载
   await loadEcommerceData();
   await loadNewMediaData();

   // 优化后：并行加载
   const [ecommerceResult, newMediaResult] = await Promise.all([
       loadEcommerceData(),
       loadNewMediaData()
   ]);
   ```

4. **用户友好的状态提示**：
   - 显示实时加载进度
   - 提供详细的操作结果反馈
   - 区分部分成功和完全失败的情况

#### 🔧 修改的文件
- `templates/distribution_plan.html` - 前端队列生成和加载逻辑优化

#### 📈 性能提升
- **队列生成速度**：提升约43%（从3.5秒降至2.0秒）
- **数据加载速度**：两个渠道同步加载，消除延迟差异
- **用户体验**：提供清晰的状态反馈，避免误导性提示

#### 🎨 用户体验改进
- ✅ 并行处理减少等待时间
- ✅ 详细的成功/失败状态反馈
- ✅ 避免误导性的错误提示
- ✅ 两个渠道数据同步加载
- ✅ 实时加载状态提示
- ✅ 智能的部分成功处理

---

### 🔧 2025-06-25 修复新媒体渠道队列生成中的免费/付费分类错误

#### 🐛 问题描述
在生成新媒体渠道的分发队列时，系统错误地将应该分配到付费队列的人员分配到了免费队列中。

**具体案例**：
- 人员：许伟
- 排班规则：`expected_free = 0`, `expected_paid = 2`
- 预期结果：许伟应该出现在新媒体渠道的付费队列中
- 实际结果：许伟错误地出现在新媒体渠道的免费队列中

#### 🔍 根本原因
原有的队列生成逻辑存在设计缺陷：
1. 先按组计算总的 `expected_free` 和 `expected_paid` 数量
2. 然后循环分配给组内所有成员，而不考虑每个成员的具体 `expected_free` 和 `expected_paid` 值
3. 这导致像许伟这样 `expected_free=0, expected_paid=2` 的成员被错误地分配到免费队列

#### ✅ 修复方案
重构队列生成算法，改为按每个成员的具体 `expected_free` 和 `expected_paid` 值来生成队列项：

1. **精确分配**：根据每个成员的 `expected_free` 值生成对应数量的免费队列项
2. **精确分配**：根据每个成员的 `expected_paid` 值生成对应数量的付费队列项
3. **电商渠道兼容**：保持电商渠道的特殊处理逻辑（`expected_free=0` 时使用 `expected_total`）
4. **详细日志**：添加详细的队列生成日志，便于调试和验证

#### 🔧 修改的文件
- `routers/distribution.py` - 修复 `/api/distribution/queue/generate-and-save` 接口的队列生成逻辑
- `routers/rule_task.py` - 修复同步版本的队列生成函数，确保一致性

#### 🎯 修复效果
- ✅ 许伟（`expected_paid=2`）现在正确出现在付费队列中
- ✅ 所有人员根据其 `expected_free` 和 `expected_paid` 值正确分配
- ✅ 生成的队列数量与排班规则中的预期数量完全一致
- ✅ 保持现有的时间段交叉排序功能不变
- ✅ 确保渠道隔离机制正常工作
- ✅ 电商渠道的队列生成不受影响

#### 📊 验证结果
修复后的队列生成逻辑能够：
- 正确识别每个成员的免费/付费接待配额
- 精确生成对应数量的队列项
- 避免错误的队列类型分配
- 提供详细的生成过程日志

---

### 2025-06-30 - 客情历史记录功能完善

#### 🎯 功能描述
完善 PUT `/api/sala_crm/{lead_id}` 接口（编辑客情记录接口），新增客情历史记录自动追加功能。

#### ✨ 新增功能
1. **客情历史记录追加**：当请求中包含 `customer_record` 字段且有值时，自动将该值追加到 `customer_history_record` 字段中
2. **JSON 格式存储**：历史记录以 JSON 格式存储，结构清晰易于解析
3. **递增索引管理**：自动管理历史记录的索引（0, 1, 2, ...）
4. **时间戳记录**：每条历史记录包含精确的时间戳（YYYY-MM-DD HH:mm:ss 格式）

#### 📊 数据结构
```json
{
  "0": {"datetime": "2025-05-24 14:54:41", "remark": "第一次客情记录"},
  "1": {"datetime": "2025-05-24 15:30:22", "remark": "第二次客情记录"},
  "2": {"datetime": "2025-05-24 16:45:33", "remark": "第三次客情记录"}
}
```

#### 🔧 技术实现
1. **智能索引计算**：自动找到现有记录的最大索引值，新记录使用 max_index + 1
2. **容错处理**：当 `customer_history_record` 字段为空或 JSON 解析失败时，从索引 0 重新开始
3. **空值过滤**：只有当 `customer_record` 不为空（去除空格后）时才执行追加操作
4. **数据库字段扩展**：将 `customer_history_record` 字段从 VARCHAR(250) 扩展为 TEXT 类型

#### 🗃️ 数据库变更
- **文件**: `migrations/schema_updates/20250630_extend_customer_history_record.sql`
- **变更**: 扩展 `sala_crm.customer_history_record` 字段类型为 TEXT
- **模型更新**: `models.py` 中 SalaCrm 模型的字段定义更新

#### 🔧 修改的文件
- `routers/sala_crm.py` - 新增客情历史记录追加逻辑
- `models.py` - 更新 customer_history_record 字段类型定义
- `migrations/schema_updates/20250630_extend_customer_history_record.sql` - 数据库迁移脚本

#### ✅ 功能特性
- ✅ 保持原有编辑客情记录功能不变
- ✅ 自动追加历史记录，无需手动管理
- ✅ JSON 格式存储，便于前端解析和展示
- ✅ 时间戳精确到秒，便于追踪记录时间
- ✅ 容错机制完善，避免数据丢失
- ✅ 空值过滤，避免无意义的空记录

---

### 2025-06-30 - 客情历史记录查询接口完善

#### 🎯 功能描述
完善 POST `/api/sala_crm/fdvalue` 接口（获取表字段的值接口），新增客情历史记录查询功能。

#### ✨ 新增功能
1. **客情历史记录查询**：当 `name` 字段为 `"customer_record"` 时，返回格式化的客情历史记录数组
2. **JSON 转数组**：将存储在数据库中的 JSON 格式历史记录转换为前端友好的数组格式
3. **智能排序**：按照数字索引顺序（0, 1, 2, ...）返回历史记录
4. **容错处理**：处理 JSON 解析异常、空值等边界情况

#### 📊 接口规范

**请求格式**：
```json
{
    "ID": "20250629000120",
    "name": "customer_record"
}
```

**响应格式**：
```json
[
    {"datetime": "2025-06-30 10:13:49", "remark": "1112233332"},
    {"datetime": "2025-06-30 10:13:58", "remark": "22222"},
    {"datetime": "2025-06-30 10:14:02", "remark": "33333"}
]
```

#### 🔧 技术实现
1. **权限验证**：只有线索对接人或超管可以查看历史记录
2. **数据转换**：将 JSON 对象按数字键排序后转换为数组
3. **异常处理**：JSON 解析失败时返回空数组 `[]`
4. **字段扩展**：支持查询其他 SalaCrm 表字段（可扩展）

#### ✅ 功能特性
- ✅ 完整的权限验证机制
- ✅ 智能的数字索引排序
- ✅ 健壮的异常处理
- ✅ 空值和边界情况处理
- ✅ 过滤非数字键，确保数据纯净
- ✅ 支持扩展其他字段查询

#### 🔧 修改的文件
- `routers/sala_crm.py` - 完善 POST `/api/sala_crm/fdvalue` 接口实现

#### 🎯 使用场景
- 前端展示客情历史记录列表
- 按时间顺序查看客情跟进记录
- 客情记录的详细查询和展示

---

### 2025-06-30 - 字段查询接口优化

#### 🎯 功能描述
优化 POST `/api/sala_crm/fdvalue` 接口，增加字段类型支持，提升接口的灵活性和扩展性。

#### ✨ 优化内容
1. **新增 type 字段**：在请求模型中添加可选的 `type` 字段，默认值为 0
2. **字段类型支持**：
   - `type=0`：客情历史记录类型（当 `name="customer_record"` 时特殊处理）
   - 其他类型：预留扩展接口
3. **灵活字段查询**：支持查询 SalaCrm 表中的任意字段
4. **向后兼容性**：保持现有功能不变，不影响已有调用

#### 📊 接口规范

**请求格式**：
```json
{
    "ID": "20250629000120",
    "name": "customer_record",
    "type": 0  // 可选字段，默认为0
}
```

**响应格式**：

*客情历史记录（name="customer_record" 且 type=0）*：
```json
[
    {"datetime": "2025-06-30 10:13:49", "remark": "第一次客情记录"},
    {"datetime": "2025-06-30 10:13:58", "remark": "第二次客情记录"}
]
```

*其他字段*：
```json
{
    "value": "字段值"
}
```

#### 🔧 技术实现
1. **条件判断优化**：只有当 `name="customer_record"` 且 `type=0` 时才执行特殊处理
2. **错误处理改进**：使用 `raise HTTPException` 替代 `return HTTPException`
3. **状态码规范化**：使用标准 HTTP 状态码（401, 403, 404, 500）
4. **参数验证增强**：分别验证 ID 和 name 字段，提供更精确的错误信息

#### ✅ 功能特性
- ✅ 完全向后兼容，不影响现有调用
- ✅ 支持字段类型扩展，为将来功能预留接口
- ✅ 灵活查询任意 SalaCrm 字段
- ✅ 改进的错误处理和状态码
- ✅ 更清晰的接口文档和参数说明

#### 🎯 使用场景
- **客情历史记录查询**：`{"ID": "xxx", "name": "customer_record", "type": 0}`
- **普通字段查询**：`{"ID": "xxx", "name": "wechat_name"}`
- **扩展类型查询**：`{"ID": "xxx", "name": "customer_record", "type": 1}` (将来扩展)

#### 🔧 修改的文件
- `routers/sala_crm.py` - 优化 POST `/api/sala_crm/fdvalue` 接口实现

---

### 2025-06-30 - 动态字段查询功能优化

#### 🎯 功能描述
进一步优化 POST `/api/sala_crm/fdvalue` 接口，实现对 SalaCrm 表中任意字段的动态查询功能，提升接口的通用性和实用性。

#### ✨ 优化内容
1. **完整的动态字段支持**：支持查询 SalaCrm 表中的所有字段
2. **字段存在性验证**：使用 `hasattr(SalaCrm, field_name)` 验证字段是否存在
3. **数据类型智能处理**：自动处理 datetime、int、str、NULL 等不同数据类型
4. **增强的错误处理**：提供更精确的错误信息和状态码
5. **完善的接口文档**：详细列出所有支持的字段

#### 📊 支持的字段列表

**基础信息字段**：
- `ID` - 线索ID
- `allocation_date` - 分配日期
- `SN` - 序号
- `wechat_name` - 微信昵称

**状态字段**：
- `is_add` - 是否添加
- `add_date` - 添加日期
- `is_billed` - 是否开单
- `is_read` - 是否已读
- `is_deleted` - 是否删除

**客户信息字段**：
- `customer_name` - 24小时内有无联系客户
- `customer_record` - 客情记录
- `customer_history_record` - 客情历史记录

**业务字段**：
- `bill_number` - 开单编号
- `reason_failure` - 添加失败原因
- `keyword_tips` - 关键词提示
- `clue_basic_tag` - 线索无效记录
- `clue_stage` - 线索阶段
- `failure_analysis` - 未成交分析

**跟进字段**：
- `self_reminder_flag` - 再次跟进提醒记录
- `last_followup_time` - 最新跟进时间
- `last_followup_record` - 无效线索记录

**广告字段**：
- `AD_PLANT_ID` - 广告计划ID
- `AD_IDEAR_ID` - 广告创意ID
- `chat_image` - 聊天截图

#### 🔧 技术实现
1. **字段验证机制**：在查询前验证字段是否存在于 SalaCrm 模型中
2. **数据类型处理**：
   - datetime 类型自动格式化为 "YYYY-MM-DD HH:mm:ss"
   - NULL 值正确返回 null
   - 其他类型保持原样
3. **特殊字段处理**：保持 `customer_record` 的历史记录查询功能
4. **错误状态码**：使用标准 HTTP 状态码（400, 401, 403, 404, 500）

#### 📊 使用示例

**查询微信昵称**：
```json
请求: {"ID": "20250629000120", "name": "wechat_name"}
响应: {"value": "张三"}
```

**查询分配日期**：
```json
请求: {"ID": "20250629000120", "name": "allocation_date"}
响应: {"value": "2025-06-30 10:30:00"}
```

**查询客情历史记录**：
```json
请求: {"ID": "20250629000120", "name": "customer_record", "type": 0}
响应: [
    {"datetime": "2025-06-30 10:13:49", "remark": "第一次客情记录"},
    {"datetime": "2025-06-30 10:13:58", "remark": "第二次客情记录"}
]
```

**查询NULL值字段**：
```json
请求: {"ID": "20250629000120", "name": "reason_failure"}
响应: {"value": null}
```

#### ✅ 功能特性
- ✅ 支持 SalaCrm 表中的所有字段动态查询
- ✅ 智能的数据类型处理和格式化
- ✅ 完善的字段存在性验证
- ✅ 保持客情历史记录的特殊处理逻辑
- ✅ 健壮的错误处理和边界情况处理
- ✅ 完全向后兼容，不影响现有功能

#### 🔧 修改的文件
- `routers/sala_crm.py` - 优化动态字段查询功能

---

## 🚀 安装与部署