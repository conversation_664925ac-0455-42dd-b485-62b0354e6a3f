from sqlalchemy import Column, Integer, String, DateTime, Boolean, JSON, Float, Text, Date, Time, ForeignKey, UniqueConstraint
from sqlalchemy.dialects.mysql import TINYINT
from sqlalchemy.orm import relationship
from database import Base
from datetime import datetime
from utils.encryption import encrypt_data, decrypt_data, is_encrypted

 # 线索表
class ClueSheet(Base):
    __tablename__ = 'clue_sheet_2025'

    ID = Column(String(50), ForeignKey("sala_crm.ID"),primary_key=True)
    record_date = Column(String(50),comment="记录日期")
    channel = Column(String(100), nullable=True,comment="渠道")
    wangwang_id = Column(String(100),comment="渠道ID")
    phone_number = Column(String(100),nullable=True,comment="电话号码")
    wechat_id = Column(String(200),nullable=True,comment="微信ID")
    store = Column(String(200),comment="店铺")
    queue = Column(String(10), nullable=True, comment="店铺类型 0免费，1付费")
    registrar = Column(String(100),comment="登记人")
    contact_person = Column(String(100),comment="对接人")
    type = Column(Integer,default=0, nullable=True,comment="线索类型 0默认，1被动，2自动，3导入")
    record_time = Column(DateTime,comment="记录时间")
    anchor = Column(String(100),comment="主播")
    shift = Column(String(50),comment="班次")
    status = Column(Integer, default=1,comment="状态 1:已提交, 2:等待中, 3:已发送")  # 1:已提交, 2:等待中, 3:已发送
    is_deleted = Column(Boolean, default=False,comment="是否删除")
    delete_time = Column(DateTime, nullable=True,comment="删除时间")
    delete_user = Column(String(50), nullable=True,comment="删除人")
    remarks = Column(String(500), comment="备注")
    not_admin = Column(String(50),nullable=True,comment="不分配销售")
    kefu_image = Column(String(200), nullable=True, comment="客服截图")
    withdraw_contact_person = Column(String(50), nullable=True, comment="撤回对接人")
    distribute_num = Column(Integer,default=1,comment="分配次数")


    # 反向关联SalaCrm
    sala_crm = relationship("SalaCrm", back_populates="clue_sheet")

    sala_crm_xx = relationship("SalaCrm", uselist=False, back_populates="clue_sheet_xx")

    # 定义与 sala_crm 的外键关系
    # sala_crm_id = Column(Integer, ForeignKey("sala_crm.id"))
    # sala_crm = relationship("SalaCrm", back_populates="clue_sheet")
    #
    # # 定义与 clue_withdraw 的外键关系
    # clue_withdraw_id = Column(Integer, ForeignKey("clue_withdraw.id"))
    # clue_withdraw = relationship("ClueWithdraw", back_populates="clue_sheet")


    # 需要加密的字段列表
    _encrypted_fields = ['phone_number', 'wechat_id']

    def encrypt_sensitive_data(self):
        """加密敏感数据"""
        for field in self._encrypted_fields:
            current_value = getattr(self, field)
            if current_value and not is_encrypted(current_value):
                encrypted_value = encrypt_data(current_value)
                setattr(self, field, encrypted_value)

    def decrypt_sensitive_data(self):
        """解密敏感数据"""
        for field in self._encrypted_fields:
            current_value = getattr(self, field)
            if current_value:
                decrypted_value = decrypt_data(current_value)
                setattr(self, field, decrypted_value)

    def to_dict(self):
        # 先解密敏感数据以返回原始值
        self.decrypt_sensitive_data()
        
        result = {
            "ID": self.ID,
            "record_date": self.record_date,
            "channel": self.channel,
            "wangwang_id": self.wangwang_id,
            "phone_number": self.phone_number,
            "wechat_id": self.wechat_id,
            "store": self.store,
            "queue": self.queue,
            "registrar": self.registrar,
            "contact_person": self.contact_person,
            "type": self.type,
            "record_time": self.record_time.isoformat() if self.record_time else None,
            "anchor": self.anchor,
            "shift": self.shift,
            "is_deleted": self.is_deleted,
            "delete_time": self.delete_time.isoformat() if self.delete_time else None,
            "delete_user": self.delete_user,
            "status": self.status,
            "remarks": self.remarks,
            "not_admin": self.not_admin,
            "kefu_image": self.kefu_image,
            "withdraw_contact_person": self.withdraw_contact_person,
            "distribute_num": self.distribute_num
        }
        
        # 重新加密敏感数据以保持数据库中的加密状态
        self.encrypt_sensitive_data()
        
        return result

# 用户表
class User(Base):
    __tablename__ = 'users'

    ID = Column(String(14), primary_key=True)
    account = Column(String(100), unique=True, nullable=False)
    password_hash = Column(String(100), nullable=False)
    contact = Column(String(130), nullable=True)
    name = Column(String(100), nullable=False)
    department = Column(String(50), nullable=False)
    created_at = Column(DateTime, nullable=False)
    is_admin = Column(Integer, nullable=False, default=3)
    group_name = Column(String(50), nullable=True)
    identity = Column(Text, nullable=True)
    wechat_openid = Column(String(50),unique=True,nullable=True)

    @staticmethod
    def generate_user_id(department: str) -> str:
        # 部门前缀映射
        dept_prefix = {
            '总经办': '10',
            '电商部': '11',
            '新媒体部': '12',
            '销售部': '13',
            '技术部': '14',
            '设计部': '15',
            '施工部': '16',
            '访客': '19'
        }
        
        # 获取当前日期（格式：YYYYMMDD）
        current_date = datetime.now().strftime('%Y%m%d')
        
        # 获取部门前缀
        prefix = dept_prefix.get(department, 'unknown')
        
        # 从数据库中获取当天最大的ID
        from sqlalchemy import text
        from database import SessionLocal
        
        db = SessionLocal()
        try:
            # 构建当天的ID前缀模式
            today_prefix = f"{prefix}{current_date}"
            # 查询当天最大的ID
            result = db.execute(
                text(f"SELECT MAX(ID) FROM users WHERE ID LIKE :prefix"),
                {"prefix": f"{today_prefix}%"}
            ).scalar()
            
            if result is None:
                sequence = 1
            else:
                # 提取序列号部分并加1
                sequence = int(str(result)[-4:]) + 1
            
            # 格式化4位序列号
            sequence_str = f"{sequence:04d}"
            
            # 返回完整的ID格式
            return f"{prefix}{current_date}{sequence_str}"
        finally:
            db.close()

    @classmethod
    def filter(cls, param):
        pass


# 用户后端权限表
class UserPermissionBackend(Base):
    __tablename__ = 'user_permissions_backend'
    
    id = Column(Integer, primary_key=True)
    department = Column(Text, nullable=False)
    identity = Column(Text, nullable=True)
    user_review = Column(Integer, nullable=False, default=1)
    review_all = Column(Integer, nullable=False, default=1)
    review_group = Column(Integer, nullable=False, default=1)
    user_management = Column(Integer, nullable=False, default=1)
    manage_all = Column(Integer, nullable=False, default=1)
    manage_group = Column(Integer, nullable=False, default=1)
    permission_manage = Column(Integer, nullable=False, default=1)
    form_preset = Column(Integer, nullable=False, default=1)
    schedule_management = Column(Integer, nullable=False, default=1)
    distribution_rules = Column(Integer, nullable=False, default=1)
    distribution_plan = Column(Integer, nullable=False, default=1)

# 用户前端权限表
class UserPermissionFrontend(Base):
    __tablename__ = 'user_permissions_frontend'
    
    id = Column(Integer, primary_key=True)
    department = Column(Text, nullable=False)
    identity = Column(Text, nullable=True)
    lead_table = Column(Integer, nullable=False, default=1)
    check_all_lead = Column(Integer, nullable=False, default=1)
    check_member_lead = Column(Integer, nullable=False, default=1)
    check_mine_lead = Column(Integer, nullable=False, default=1)
    edit_all_lead = Column(Integer, nullable=False, default=1)
    edit_member_lead = Column(Integer, nullable=False, default=1)
    edit_mine_lead = Column(Integer, nullable=False, default=1)
    forbid_edit = Column(Integer, nullable=False, default=1)
    delete_all_lead = Column(Integer, nullable=False, default=1)
    delete_member_lead = Column(Integer, nullable=False, default=1)
    delete_mine_lead = Column(Integer, nullable=False, default=1)
    export_lead_table = Column(Integer, nullable=False, default=1)
    import_lead_table = Column(Integer, nullable=False, default=1)
    lead_submit = Column(Integer, nullable=False, default=1)
    recycle_bin = Column(Integer, nullable=False, default=1)
    check_all_bin = Column(Integer, nullable=False, default=1)
    check_member_bin = Column(Integer, nullable=False, default=1)
    check_mine_bin = Column(Integer, nullable=False, default=1)
    user_messages = Column(Integer, nullable=False, default=1)
    check_all_messages = Column(Integer, nullable=False, default=1)
    check_member_messages = Column(Integer, nullable=False, default=1)
    check_mine_messages = Column(Integer, nullable=False, default=1)
    #查看客户页
    lead_crm = Column(Integer, nullable=False, default=1)
    check_all_crm = Column(Integer, nullable=False, default=1)
    check_group_crm = Column(Integer, nullable=False, default=1)
    check_person_crm = Column(Integer, nullable=False, default=1)
    #审批页面
    check_shenpi = Column(Integer, nullable=False, default=1)
    check_all_shenpi = Column(Integer, nullable=False, default=1)
    check_mine_shenpi = Column(Integer, nullable=False, default=1)
    #公海页面
    openseas_rule = Column(Integer, nullable=False, default=1)
    openseas = Column(Integer, nullable=False, default=1)
    openseas_all = Column(Integer, nullable=False, default=1)
    openseas_group = Column(Integer, nullable=False, default=1)
    openseas_mine = Column(Integer, nullable=False, default=1)


# 排班管理相关模型
class Channel(Base):
    """渠道表"""
    __tablename__ = "channels"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(100), nullable=False, comment="渠道名称")
    description = Column(String(255), nullable=True, comment="渠道描述")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

class Store(Base):
    """店铺表"""
    __tablename__ = "stores"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(100), nullable=False, comment="店铺名称")
    channel_id = Column(Integer, ForeignKey("channels.id"), nullable=True, comment="所属渠道ID")
    description = Column(String(255), nullable=True, comment="店铺描述")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    channel = relationship("Channel", backref="stores")
    
    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "channel_id": self.channel_id,
            "channel_name": self.channel.name if self.channel else None,
            "description": self.description,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

class Anchor(Base):
    """主播表"""
    __tablename__ = "anchors"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(100), nullable=False, comment="主播姓名")
    contact = Column(String(100), nullable=True, comment="联系方式")
    status = Column(String(20), default="active", comment="状态：active-活跃，inactive-非活跃")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "contact": self.contact,
            "status": self.status,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

class Schedule(Base):
    """排班表"""
    __tablename__ = "schedules"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    date = Column(Date, nullable=False, comment="排班日期")
    anchor_id = Column(String(100), nullable=False, comment="主播ID或名称")
    store_id = Column(String(100), nullable=False, comment="店铺ID或名称")
    channel_id = Column(String(100), nullable=False, comment="渠道ID或名称")
    shift = Column(String(20), nullable=False, comment="班次：morning-早班，evening-晚班")
    start_time = Column(Time, nullable=False, comment="开始时间")
    end_time = Column(Time, nullable=False, comment="结束时间")
    duration = Column(Float, nullable=False, comment="时长(小时)")
    rest_duration = Column(Float, nullable=True, default=1.5, comment="休息时长(小时)")
    room = Column(String(100), nullable=True, comment="直播间")
    expected_count = Column(Integer, nullable=True, default=0, comment="预计数量")
    real_count = Column(Integer, nullable=True, default=0, comment="实际数量")
    live_count = Column(Integer, nullable=True, default=0, comment="直播数量")
    notes = Column(String(255), nullable=True, comment="备注")
    created_by = Column(String(100), nullable=True, comment="创建人")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        return {
            "id": self.id,
            "date": self.date.isoformat() if self.date else None,
            "anchor_id": self.anchor_id,
            "store_id": self.store_id,
            "channel_id": self.channel_id,
            "shift": self.shift,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "duration": self.duration,
            "rest_duration": self.rest_duration,
            "room": self.room,
            "expected_count": self.expected_count,
            "real_count": self.real_count,
            "live_count": self.live_count,
            "notes": self.notes,
            "created_by": self.created_by,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

class DistributionRule(Base):
    __tablename__ = 'distribution_rules'

    id = Column(Integer, primary_key=True)
    date = Column(Date, nullable=False, index=True)  # 规则日期
    channel_type = Column(String(20), nullable=False)  # 渠道类型：电商渠道、新媒体渠道、公海渠道
    #queue_type = Column(String(10), nullable=False, default='free',comment="队列类型：free-免费队列，paid-付费队列")
    group_name = Column(String(50), nullable=False)  # 分组名称
    leader = Column(String(50), nullable=False)  # 负责人
    member = Column(String(50), nullable=False)  # 成员
    on_duty_count = Column(Integer, nullable=False, default=0)  # 在岗人数
    shift = Column(String(10), nullable=False)  # 班次：白班、晚班、全天
    expected_total = Column(Integer, nullable=False, default=0)  # 预计接待数
    expected_free = Column(Integer, nullable=False, default=0)  # 预计免费接待数
    expected_paid = Column(Integer, nullable=False, default=0)  # 预计付费接待数
    actual_total = Column(Integer, nullable=True)  # 实际接待数
    actual_free = Column(Integer, nullable=True)  # 实际免费接待数
    actual_paid = Column(Integer, nullable=True)  # 实际付费接待数
    store = Column(String(100), nullable=False)  # 店铺
    paid_value = Column(Float, nullable=True)  # 付费产值
    time_range = Column(String(50), nullable=False)  # 时间范围
    remark = Column(String(200), nullable=True)  # 备注
    created_at = Column(DateTime, nullable=False, default=datetime.now)
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now)
    time_range_start = Column(DateTime, nullable=False)
    time_range_end = Column(DateTime, nullable=False)
    is_deleted = Column(Integer,nullable=False,default=0,comment="是否删除")
  

    # 创建联合唯一索引，确保每个日期、渠道类型和分组名称的组合只有一条记录
    __table_args__ = (
        UniqueConstraint('date', 'channel_type', 'group_name', name='unique_group_distribution'),
    )

#排班统计表
class ChannelReception(Base):
    __tablename__ = 'channel_receptions'

    ID = Column(String(36), primary_key=True)
    rule_date = Column(Date, nullable=False, index=True)  # 规则日期
    channel_type = Column(String(20), nullable=False)  # 渠道类型：电商渠道、新媒体渠道、公海渠道
    expected_reception = Column(Integer, nullable=False, default=0)  # 预计接待数
    expected_free_reception = Column(Integer, nullable=False, default=0)  # 预计免费接待数
    expected_paid_reception = Column(Integer, nullable=False, default=0)  # 预计付费接待数
    actual_reception = Column(Integer, nullable=True)  # 实际接待数
    actual_free_reception = Column(Integer, nullable=True)  # 实际免费接待数
    actual_paid_reception = Column(Integer, nullable=True)  # 实际付费接待数
    created_at = Column(DateTime, nullable=False, default=datetime.now)
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now)

class GroupDistributionRatio(Base):
    __tablename__ = 'group_distribution_ratios'

    id = Column(Integer, primary_key=True, autoincrement=True)
    rule_date = Column(Date, nullable=False, index=True)  # 规则日期
    channel_type = Column(String(20), nullable=False)  # 渠道类型：电商渠道、新媒体渠道
    group_name = Column(String(50), nullable=False)  # 分组名称
    distribution_ratio = Column(Integer, nullable=False, default=0)  # 分配数量
    created_at = Column(DateTime, nullable=False, default=datetime.now)
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now)

    # 创建联合唯一索引，确保每个日期、渠道类型和分组名称的组合只有一条记录
    __table_args__ = (
        UniqueConstraint('rule_date', 'channel_type', 'group_name', name='uix_group_distribution_ratio'),
    )

class DingTalkGroup(Base):
    """钉钉群聊信息表"""
    __tablename__ = "dingtalk_groups"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    chat_id = Column(String(100), nullable=False, unique=True, comment="群聊ID")
    chat_name = Column(String(100), nullable=False, comment="群聊名称")
    group_id = Column(String(50), nullable=True, comment="分组ID")
    webhook_url = Column(String(500), nullable=False, comment="Webhook URL")
    secret = Column(String(200), nullable=True, comment="加签密钥")
    owner_name = Column(String(100), nullable=True, comment="群主姓名")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        return {
            "id": self.id,
            "chat_id": self.chat_id,
            "chat_name": self.chat_name,
            "group_id": self.group_id,
            "webhook_url": self.webhook_url,
            "secret": self.secret,
            "owner_name": self.owner_name,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

class DistributionQueue(Base):
    """分发队列表"""
    __tablename__ = "distribution_queues"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    queue_date = Column(Date, nullable=False, index=True, comment="队列日期")
    channel_type = Column(String(20), nullable=False, comment="渠道类型：电商渠道、新媒体渠道、公海渠道")
    queue_type = Column(String(10), nullable=False, comment="队列类型：free-免费队列，paid-付费队列")
    position = Column(Integer, nullable=False, comment="队列位置")
    group_name = Column(String(50), nullable=False, comment="分组名称")
    leader = Column(String(50), nullable=False, comment="负责人")
    member = Column(String(40), nullable=False, comment="成员")
    time_slot = Column(String(50), nullable=False, comment="时间段")
    status = Column(String(20), default="待分发", comment="状态：待分发、已分发、已取消等")
    distributed_at = Column(DateTime, nullable=True, comment="分发时间")
    distributed_by = Column(String(50), nullable=True, comment="分发人")
    remark = Column(String(255), nullable=True, comment="备注")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    email_seeded = Column(TINYINT, default=0, comment="是否发送邮箱")
    time_slot_beg = Column(DateTime, default=datetime.utcnow)
    time_slot_end = Column(DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            "id": self.id,
            "queue_date": self.queue_date.isoformat() if self.queue_date else None,
            "channel_type": self.channel_type,
            "queue_type": self.queue_type,
            "position": self.position,
            "group_name": self.group_name,
            "leader": self.leader,
            "member": self.member,
            "time_slot": self.time_slot,
            "status": self.status,
            "distributed_at": self.distributed_at.isoformat() if self.distributed_at else None,
            "distributed_by": self.distributed_by,
            "remark": self.remark,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "email_seeded": self.email_seeded,
            "time_slot_beg": self.time_slot_beg.isoformat() if self.time_slot_beg else None,
            "time_slot_end": self.time_slot_end.isoformat() if self.time_slot_end else None,
        }

class DingTalkGroupMember(Base):
    """钉钉群聊成员信息表"""
    __tablename__ = "dingtalk_group_members"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    member_id = Column(String(100), nullable=False, unique=True, comment="成员ID")
    name = Column(String(100), nullable=False, comment="成员姓名")
    group_id = Column(String(50), nullable=False, comment="所属分组ID")
    position = Column(String(100), nullable=True, comment="职位")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        return {
            "id": self.id,
            "member_id": self.member_id,
            "name": self.name,
            "group_id": self.group_id,
            "position": self.position,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

class MonitoringStatus(Base):
    """监测状态表"""
    __tablename__ = "monitoring_status"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    is_enabled = Column(Boolean, default=True, comment="是否启用监测")
    last_updated = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="最后更新时间")
    
    def to_dict(self):
        return {
            "id": self.id,
            "is_enabled": self.is_enabled,
            "last_updated": self.last_updated.isoformat() if self.last_updated else None
        }

class FormattedData(Base):
    """格式化数据表"""
    __tablename__ = "formatted_data"
    
    id = Column(String(100), primary_key=True, comment="数据ID")
    raw_data = Column(Text, nullable=False, comment="原始数据JSON字符串")
    formatted_text = Column(Text, nullable=True, comment="格式化后的文本")
    template_id = Column(String(50), nullable=True, comment="使用的模板ID")
    is_sent = Column(Boolean, default=False, comment="是否已发送")
    sent_at = Column(DateTime, nullable=True, comment="发送时间")
    sent_to = Column(String(100), nullable=True, comment="发送目标")
    process_time = Column(Float, nullable=True, comment="处理时间(毫秒)")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    
    def to_dict(self):
        return {
            "id": self.id,
            "raw_data": self.raw_data,
            "formatted_text": self.formatted_text,
            "template_id": self.template_id,
            "is_sent": self.is_sent,
            "sent_at": self.sent_at.isoformat() if self.sent_at else None,
            "sent_to": self.sent_to,
            "process_time": self.process_time,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

class FormattingTemplate(Base):
    """格式化模板表"""
    __tablename__ = "formatting_templates"
    
    id = Column(String(50), primary_key=True, comment="模板ID")
    name = Column(String(100), nullable=False, comment="模板名称")
    content = Column(Text, nullable=False, comment="模板内容")
    prefix = Column(String(200), nullable=True, comment="前缀")
    suffix = Column(String(200), nullable=True, comment="后缀")
    line_break = Column(String(10), default="\n", comment="换行符")
    created_by = Column(String(50), nullable=True, comment="创建人")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "content": self.content,
            "prefix": self.prefix,
            "suffix": self.suffix,
            "line_break": self.line_break,
            "created_by": self.created_by,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


# 邮箱发送表
class Emailseed(Base):
    __tablename__ = 'emailseed'

    id = Column(Integer, primary_key=True, autoincrement=True)
    email = Column(String(50), unique=False, nullable=False,comment="邮箱")
    code = Column(Integer, nullable=True,comment="验证码")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")

    def to_dict(self):
        return {
            "id": self.id,
            "email": self.email,
            "code": self.code,
            "created_at": self.created_at,
        }


# 客户关系管理表
class SalaCrm(Base):
    __tablename__ = 'sala_crm'

    ID = Column(String(30),primary_key=True,comment="线索ID")
    allocation_date = Column(DateTime, nullable=True, comment="分配日期")#（时间，需要包含年 + 月 + 日 + 时间点）
    SN = Column(Integer,default=0,comment="序号")
    wechat_name = Column(String(80), nullable=True, comment="微信昵称")
    add_date = Column(DateTime, comment="添加日期")#（时间，需要包含年 + 月 + 日 + 时间点）
    is_add = Column(TINYINT, default=0, comment="是否添加")  # 0表示未处理，1表示添加成功，2表示添加失败
    reason_failure = Column(String(50), nullable=True, comment="添加失败失败原因")
    customer_name = Column(Integer,nullable=True,comment="24小时内有无联系客户")
    is_billed = Column(TINYINT, nullable=True,comment="是否开单")
    bill_number = Column(String(30), nullable=True, comment="开单编号")
    bill_riqi = Column(DateTime, comment="开单日期")#（时间，需要包含年 + 月 + 日 + 时间点）
    bill_use_time = Column(Float, nullable=True, comment="开单用时")
    customer_record = Column(String(50), nullable=True, comment="客情记录")
    customer_record_images = Column(String(250), nullable=True, comment="客情记录图片")

    keyword_tips = Column(String(40), nullable=True, comment="关键词提示")
    clue_stage = Column(String(50), nullable=True, comment="线索阶段")
    self_reminder_flag = Column(String(50), nullable=True, comment="再次跟进提醒记录")
    last_followup_time = Column(DateTime, comment="最新跟进时间")#（时间，需要包含年 + 月 + 日 + 时间点）
    clue_basic_tag = Column(String(40), nullable=True, comment="线索无效记录")
    last_followup_record = Column(String(50), nullable=True, comment="无效线索记录(管理)")
    failure_analysis = Column(String(50), nullable=True, comment="未成交分析")
    is_read = Column(TINYINT, default=0, comment="是否已读")
    AD_PLANT_ID = Column(String(70), nullable=True, comment="广告计划ID")
    AD_IDEAR_ID = Column(String(70), nullable=True, comment="广告创意ID")
    is_deleted = Column(TINYINT, default=0, comment="是否删除")
    clue_stage_follow_time = Column(DateTime,comment="触发时间")
    clue_stage_follow_seeded = Column(Integer,default=0,comment="是否跟进通知")
    chat_image = Column(String(250), nullable=True, comment="聊天截图")
    zu_customer_name = Column(TINYINT, nullable=True, comment="24小时内有无联系客户(组长)")
    s_demand_feature = Column(String(120), nullable=True, comment="S级强需求(特征)")
    a_demand_feature = Column(String(120), nullable=True, comment="A级需求不明确(特征)")
    b_demand_feature = Column(String(120), nullable=True, comment="B级低需求(特征)")
    c_demand_feature = Column(String(120), nullable=True, comment="C级无需求(特征)")
   
    s_zu_demand_feature = Column(String(120), nullable=True, comment="S级强需求(特征)(组长)")
    a_zu_demand_feature = Column(String(120), nullable=True, comment="A级需求不明确(特征)(组长)")
    b_zu_demand_feature = Column(String(120), nullable=True, comment="B级低需求(特征)(组长)")
    c_zu_demand_feature = Column(String(120), nullable=True, comment="C级无需求(特征)(组长)")
    os_status = Column(TINYINT, default=0, comment="是否是公海数据")

    # 定义一对一关系，uselist=False 表示一对一
    clue_sheet = relationship("ClueSheet", uselist=False, back_populates="sala_crm")
    clue_sheet_xx = relationship("ClueSheet", back_populates="sala_crm_xx")

    #clue_withdraw = relationship("ClueWithdraw",uselist=False, back_populates="sala_crm")

    def to_dict(self):
        return {
            "ID": self.ID,
            "allocation_date": self.allocation_date,
            "SN": self.SN,
            "wechat_name": self.wechat_name,
            "add_date": self.add_date,
            "is_add": self.is_add,
            "reason_failure": self.reason_failure,
            "customer_name": self.customer_name,
            "is_billed": self.is_billed,
            "bill_number": self.bill_number,
            "bill_riqi": self.bill_riqi,
            "bill_use_time": self.bill_use_time,
            "customer_record": self.customer_record,
            "customer_record_images": self.customer_record_images,
            "keyword_tips": self.keyword_tips,
            "clue_stage": self.clue_stage,
            "self_reminder_flag": self.self_reminder_flag,
            "last_followup_time": self.last_followup_time,
            "clue_basic_tag": self.clue_basic_tag,
            "last_followup_record": self.last_followup_record,
            "failure_analysis": self.failure_analysis,
            "is_read": self.is_read,
            # "AD_PLANT_ID": self.AD_PLANT_ID,
            # "AD_IDEAR_ID": self.AD_IDEAR_ID,
            "clue_stage_follow_time": self.clue_stage_follow_time,
            "chat_image": self.chat_image,
            "zu_customer_name": self.zu_customer_name,
            "s_demand_feature": self.s_demand_feature,
            "a_demand_feature": self.a_demand_feature,
            "b_demand_feature": self.b_demand_feature,
            "c_demand_feature": self.c_demand_feature,
            "s_zu_demand_feature": self.s_zu_demand_feature,
            "a_zu_demand_feature": self.a_zu_demand_feature,
            "b_zu_demand_feature": self.b_zu_demand_feature,
            "c_zu_demand_feature": self.c_zu_demand_feature,
        }

# 客户关系历史记录管理表
class SalaCrmHistory(Base):
    __tablename__ = 'sala_crm_history'

    ID = Column(String(80),primary_key=True,comment="线索ID")
    s_demand_feature_history = Column(Text, nullable=True, comment="S级强需求历史(特征)")
    a_demand_feature_history = Column(Text, nullable=True, comment="A级需求不明确历史(特征)")
    b_demand_feature_history = Column(Text, nullable=True, comment="B级低需求历史(特征)")
    c_demand_feature_history = Column(Text, nullable=True, comment="C级无需求历史(特征)")
    s_zu_demand_feature_history = Column(Text, nullable=True, comment="S级强需求历史(特征)(组长)")
    a_zu_demand_feature_history = Column(Text, nullable=True, comment="A级需求不明确历史(特征)(组长)")
    b_zu_demand_feature_history = Column(Text, nullable=True, comment="B级低需求历史(特征)(组长)")
    c_zu_demand_feature_history = Column(Text, nullable=True, comment="C级无需求历史(特征)(组长)")
    self_reminder_history_flag = Column(Text, nullable=True, comment="再次跟进提醒历史记录")
    customer_history_record = Column(Text, nullable=True, comment="客情历史记录")
  
    def to_dict(self):
        return {
            "ID": self.ID,
            "s_demand_feature": self.s_demand_feature,
            "a_demand_feature": self.a_demand_feature,
            "b_demand_feature": self.b_demand_feature,
            "c_demand_feature": self.c_demand_feature,
            "s_zu_demand_feature": self.s_zu_demand_feature,
            "a_zu_demand_feature": self.a_zu_demand_feature,
            "b_zu_demand_feature": self.b_zu_demand_feature,
            "c_zu_demand_feature": self.c_zu_demand_feature,
            "self_reminder_history_flag": self.self_reminder_history_flag,
            "customer_history_record": self.customer_history_record,
        }
    
# 线索撤回表
class ClueWithdraw(Base):
    __tablename__ = 'clue_withdraw'

    ID = Column(String(80),primary_key=True,comment="线索ID")
    contact_person = Column(String(40), nullable=True, comment="申请人")
    withdraw_reason = Column(String(50), nullable=True, comment="撤回原因")
    withdraw_evidence = Column(String(200),nullable=True, comment="撤回证据")
    reviewer = Column(String(40), nullable=True, comment="审批人")
    withdraw_status = Column(TINYINT, default=0, comment="审批状态")  # 0待审批，1已批准，2已拒绝
    apply_time = Column(DateTime, nullable=True,comment="申请时间")#（时间，需要包含年 + 月 + 日 + 时间点）
    shenpi_time = Column(DateTime, nullable=True,comment="审批时间")#（时间，需要包含年 + 月 + 日 + 时间点）
    receipt = Column(String(100), nullable=True, comment="回执")
    approval_type = Column(TINYINT, default=0, comment="审批类型")
    apply_num = Column(TINYINT, default=0, comment="审批次数")
    is_tongzhi = Column(TINYINT, default=0, comment="是否通知")
    handle_type = Column(TINYINT, default=0, comment="处理类型") #处理类型  2:重新分发,5标记撤回


    # 定义一对一关系，uselist=False 表示一对一
    #clue_sheet = relationship("ClueSheet", uselist=False, back_populates="clue_withdraw")

    def to_dict(self):
        return {
            "ID": self.ID,
            "contact_person": self.contact_person,
            "withdraw_reason": self.withdraw_reason,
            "withdraw_evidence": self.withdraw_evidence,
            "reviewer": self.reviewer,
            "withdraw_status": self.withdraw_status,
            "apply_time": self.apply_time,
            "shenpi_time": self.shenpi_time,
            "receipt": self.receipt,
            "approval_type": self.approval_type,
            "apply_num": self.apply_num,
            "is_tongzhi": self.is_tongzhi,
            "handle_type": self.handle_type,
        }


# 订单编号生成表
class OrderBillNumber(Base):
    __tablename__ = 'order_bill_code'

    id = Column(Integer, primary_key=True, autoincrement=True)
    sala_id = Column(String(50), unique=True, nullable=True,comment="客情ID")
    orderno = Column(String(40), unique=True, nullable=True,comment="开单编号数字")
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")

    def to_dict(self):
        return {
            "id": self.id,
            "sala_id": self.sala_id,
            "orderno": self.orderno,
            "created_at": self.created_at,
        }
    
# 分类表
class Category(Base):
    __tablename__ = 'category'

    id = Column(Integer, primary_key=True, autoincrement=True)
    pid = Column(Integer,default=0, unique=False, nullable=True,comment="父级ID")
    type = Column(String(30), unique=False, nullable=True,comment="类型")
    name = Column(String(30), unique=False, nullable=True,comment="名称")
    weight = Column(Integer,default=0, unique=False, nullable=True,comment="权重")
    status = Column(TINYINT, default=1, unique=False, nullable=True,comment="状态")

    def to_dict(self):
        return {
            "id": self.id,
            "pid": self.pid,
            "type": self.type,
            "name": self.name,
            "weight": self.weight,
            "status": self.status,
        }


# 分发队列生成启动表
class DistributionRulesTask(Base):
    __tablename__ = 'distribution_rules_task'

    id = Column(Integer, primary_key=True, autoincrement=True)
    date_time = Column(DateTime, default=datetime.utcnow, comment="启动时间")
    status = Column(TINYINT, default=0, unique=False, nullable=True,comment="状态")
    def to_dict(self):
        return {
            "id": self.id,
            "date_time": self.date_time,
            "status": self.status,
        }
    

# 用户配置表
class UserConfig(Base):
    __tablename__ = 'user_config'

    ID = Column(String(36),primary_key=True,comment="用户ID")
    tzstatus = Column(TINYINT, default=1, unique=False, nullable=True,comment="系统通知")
    nodisturbing = Column(TINYINT, default=1, unique=False, nullable=True,comment="免打扰")
    beg = Column(String(30),default="22:00",nullable=True,comment="免打扰时段")
    end = Column(String(30),default="08:00",nullable=True,comment="免打扰时段")

    def to_dict(self):
        return {
            "ID": self.ID,
            "tzstatus": self.tzstatus,
            "nodisturbing": self.nodisturbing,
            "beg": self.beg,
            "beg": self.end,
        }
    

# 公海客户关系管理表
class SalaCrmOpenseas(Base):
    __tablename__ = 'sala_crm_openseas'

    ID = Column(String(30),primary_key=True,comment="线索ID")
    contact_person_openseas = Column(String(50), nullable=True, comment="现对接人")
    allocation_date = Column(DateTime, nullable=True, comment="分配日期")#（时间，需要包含年 + 月 + 日 + 时间点）
    SN = Column(Integer,default=0,comment="序号")
    wechat_name = Column(String(80), nullable=True, comment="微信昵称")
    add_date = Column(DateTime, comment="添加日期")#（时间，需要包含年 + 月 + 日 + 时间点）
    is_add = Column(TINYINT, default=0, comment="是否添加")  # 0表示未处理，1表示添加成功，2表示添加失败
    reason_failure = Column(String(50), nullable=True, comment="添加失败失败原因")
    customer_name = Column(Integer,nullable=True,comment="24小时内有无联系客户")
    is_billed = Column(TINYINT, nullable=True,comment="是否开单")
    bill_number = Column(String(30), nullable=True, comment="开单编号")
    bill_riqi = Column(DateTime, comment="开单日期")#（时间，需要包含年 + 月 + 日 + 时间点）
    bill_use_time = Column(Float, nullable=True, comment="开单用时")
    customer_record = Column(String(50), nullable=True, comment="客情记录")
    customer_record_images = Column(String(250), nullable=True, comment="客情记录图片")

    keyword_tips = Column(String(40), nullable=True, comment="关键词提示")
    clue_stage = Column(String(50), nullable=True, comment="线索阶段")
    self_reminder_flag = Column(String(50), nullable=True, comment="再次跟进提醒记录")
    last_followup_time = Column(DateTime, comment="最新跟进时间")#（时间，需要包含年 + 月 + 日 + 时间点）
    clue_basic_tag = Column(String(40), nullable=True, comment="线索无效记录")
    last_followup_record = Column(String(50), nullable=True, comment="无效线索记录(管理)")
    failure_analysis = Column(String(50), nullable=True, comment="未成交分析")
    is_read = Column(TINYINT, default=0, comment="是否已读")
    is_deleted = Column(TINYINT, default=0, comment="是否删除")
    clue_stage_follow_time = Column(DateTime,comment="触发时间")
    clue_stage_follow_seeded = Column(Integer,default=0,comment="是否跟进通知")
    chat_image = Column(String(250), nullable=True, comment="聊天截图")
    zu_customer_name = Column(TINYINT, nullable=True, comment="24小时内有无联系客户(组长)")
    s_demand_feature = Column(String(120), nullable=True, comment="S级强需求(特征)")
    a_demand_feature = Column(String(120), nullable=True, comment="A级需求不明确(特征)")
    b_demand_feature = Column(String(120), nullable=True, comment="B级低需求(特征)")
    c_demand_feature = Column(String(120), nullable=True, comment="C级无需求(特征)")
   
    s_zu_demand_feature = Column(String(120), nullable=True, comment="S级强需求(特征)(组长)")
    a_zu_demand_feature = Column(String(120), nullable=True, comment="A级需求不明确(特征)(组长)")
    b_zu_demand_feature = Column(String(120), nullable=True, comment="B级低需求(特征)(组长)")
    c_zu_demand_feature = Column(String(120), nullable=True, comment="C级无需求(特征)(组长)")
    openseas_status = Column(TINYINT, default=0, comment="状态 0:待处理，1:已分发")
    create_time = Column(DateTime, nullable=True,default=datetime.utcnow, comment="进入时间")


    def to_dict(self):
        return {
            "ID": self.ID,
            "contact_person_openseas": self.contact_person_openseas,
            "allocation_date": self.allocation_date,
            "SN": self.SN,
            "wechat_name": self.wechat_name,
            "add_date": self.add_date,
            "is_add": self.is_add,
            "reason_failure": self.reason_failure,
            "customer_name": self.customer_name,
            "is_billed": self.is_billed,
            "bill_number": self.bill_number,
            "bill_riqi": self.bill_riqi,
            "bill_use_time": self.bill_use_time,
            "customer_record": self.customer_record,
            "customer_record_images": self.customer_record_images,
            "keyword_tips": self.keyword_tips,
            "clue_stage": self.clue_stage,
            "self_reminder_flag": self.self_reminder_flag,
            "last_followup_time": self.last_followup_time,
            "clue_basic_tag": self.clue_basic_tag,
            "last_followup_record": self.last_followup_record,
            "failure_analysis": self.failure_analysis,
            "is_read": self.is_read,
            "clue_stage_follow_time": self.clue_stage_follow_time,
            "chat_image": self.chat_image,
            "zu_customer_name": self.zu_customer_name,
            "s_demand_feature": self.s_demand_feature,
            "a_demand_feature": self.a_demand_feature,
            "b_demand_feature": self.b_demand_feature,
            "c_demand_feature": self.c_demand_feature,
            "s_zu_demand_feature": self.s_zu_demand_feature,
            "a_zu_demand_feature": self.a_zu_demand_feature,
            "b_zu_demand_feature": self.b_zu_demand_feature,
            "c_zu_demand_feature": self.c_zu_demand_feature,
            "create_time": self.create_time,
        }
    

# 客情表触发条件进公海的中间表
class SalaCrmToOpenseas(Base):
    __tablename__ = 'sala_crm_to_openseas'
    id = Column(Integer, primary_key=True, autoincrement=True)
    lead_id = Column(String(40),comment="线索ID")
    fname = Column(String(40), nullable=True, comment="名称")
    ftime = Column(DateTime,nullable=True, comment="触发时间")
    is_trans = Column(TINYINT, default=0,nullable=False,comment="是否进公海 0:未进，1已进")
    switch_stages = Column(TINYINT, nullable=True,default=1, comment="转换策略 (0:组内转换，1:跨组转换)")
    cf_contact_person = Column(String(50),default=0, nullable=True, comment="对接人")
    is_delete = Column(TINYINT, default=0,nullable=False,comment="是否删除 0:未删，1已删")

    def to_dict(self):
        return {
            "id":self.id,
            "lead_id": self.lead_id,
            "fname": self.fname,
            "ftime": self.ftime,
            "is_trans": self.is_trans,
            "switch_stages": self.switch_stages,
            "cf_contact_person": self.cf_contact_person,
        }
    
# 公海历史记录管理表
class SalaCrmHistoryOpenseas(Base):
    __tablename__ = 'sala_crm_history_openseas'
    id = Column(Integer, primary_key=True, autoincrement=True)
    lead_id = Column(String(40),comment="线索ID")
    fname = Column(String(40), nullable=True, comment="名称")
    fvalue = Column(Text, nullable=True, comment="记录")  # 修正字段名：fvname -> fvalue
    create_time = Column(DateTime,nullable=True, comment="创建时间")

    def to_dict(self):
        return {
            "id":self.id,
            "lead_id": self.lead_id,
            "fname": self.fname,
            "fvalue": self.fvalue,  # 修正字段名：fvname -> fvalue
            "create_time": self.create_time,
        }
    
 
# 公海转换规则配置表
class RulesOpenseas(Base):
    __tablename__ = 'rules_openseas'
    id = Column(Integer, primary_key=True, autoincrement=True)
    weigh = Column(Integer,default=99,comment="优先级")
    rule_type = Column(TINYINT, default=0, comment="规则类型（0：默认条件，1：特定条件，2：排除条件）")
    channel = Column(String(30), nullable=True, comment="渠道")
    queue = Column(TINYINT, nullable=True, comment="线索类型( 0付费店铺，1免费店铺)")
    fname = Column(String(40), nullable=True, comment="字段名称")
    tip = Column(String(10), nullable=True, comment="条件符号")
    fvalue = Column(String(50), nullable=True, comment="内容")
    hours = Column(Integer, default=0, comment="时间(H)")
    switch_stages = Column(TINYINT, nullable=True, comment="转换策略 (0:组内转换，1:跨组转换)")
    cf_contact_person = Column(TINYINT, default=0, comment="对接人重复 0:不能，1:可以")
    remarks = Column(String(120), nullable=True, comment="说明")
    is_delete = Column(TINYINT, default=0,nullable=False,comment="是否删除 0:未删，1已删")
   
    def to_dict(self):
        return {
            "id":self.id,
            "weigh": self.weigh,
            "rule_type": self.rule_type,
            "channel": self.channel,
            "queue": self.queue,
            "fname": self.fname,
            "fvalue": self.fvalue,
            "tip": self.tip,
            "hours": self.hours,
            "switch_stages": self.switch_stages,
            "cf_contact_person": self.cf_contact_person,
            "remarks": self.remarks,
        }