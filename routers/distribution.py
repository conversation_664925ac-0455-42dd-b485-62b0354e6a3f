# -*- coding: utf-8 -*-
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from database import get_db
from models import DistributionRule, ChannelReception, User, GroupDistributionRatio,DistributionQueue,ClueSheet,SalaCrm
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from datetime import date, time, datetime, timedelta
import uuid
from auth import get_current_user, base64_decrypt_data,aoksend_send_email
import heapq
from collections import defaultdict
import httpx
from fastapi.background import BackgroundTasks
from routers.sala_crm import update_sala_crm
import os

router = APIRouter()
WEB_URL = os.getenv("WEB_URL")

# 定义请求和响应模型
class DistributionRuleBase(BaseModel):
    channel_type: str
    group_name: str
    leader: str
    member: str
    shift: str
    expected_reception: int  # 保持原字段名，但在处理时映射到expected_total
    expected_free_reception: int  # 保持原字段名，但在处理时映射到expected_free
    expected_paid_reception: int  # 保持原字段名，但在处理时映射到expected_paid
    store: str
    time_range_start: time  # 保持原字段名，但在处理时转换为time_range
    time_range_end: time  # 保持原字段名，但在处理时转换为time_range
    remarks: Optional[str] = None  # 保持原字段名，但在处理时映射到remark

class DistributionRuleCreate(DistributionRuleBase):
    rule_date: date  # 保持原字段名，但在处理时映射到date

class DistributionRuleUpdate(BaseModel):
    channel_type: Optional[str] = None
    group_name: Optional[str] = None
    leader: Optional[str] = None
    member: Optional[str] = None
    shift: Optional[str] = None
    expected_reception: Optional[int] = None # 保持原字段名，但在处理时映射到expected_total
    expected_free_reception: Optional[int] = None # 保持原字段名，但在处理时映射到expected_free
    expected_paid_reception: Optional[int] = None   # 保持原字段名，但在处理时映射到expected_paid
    store: Optional[str] = None
    time_range_start: Optional[str] = None
    time_range_end: Optional[str] = None
    remarks: Optional[str] = None  # 保持原字段名，但在处理时映射到remark


class DistributionRuleResponse(BaseModel):
    id: int
    date: date
    channel_type: str
    group_name: str
    leader: str
    member: str
    on_duty_count: int
    shift: str
    expected_total: int
    expected_free: int
    expected_paid: int
    actual_total: Optional[int] = None
    actual_free: Optional[int] = None
    actual_paid: Optional[int] = None
    store: str
    paid_value: Optional[float] = None
    time_range: str
    remark: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    time_range_start: datetime
    time_range_end: datetime

class ChannelReceptionBase(BaseModel):
    channel_type: str
    expected_reception: int
    expected_free_reception: int
    expected_paid_reception: int

class ChannelReceptionCreate(ChannelReceptionBase):
    rule_date: date

class ChannelReceptionUpdate(ChannelReceptionBase):
    pass

class ChannelReceptionResponse(ChannelReceptionBase):
    ID: str
    rule_date: date
    actual_reception: Optional[int] = None
    actual_free_reception: Optional[int] = None
    actual_paid_reception: Optional[int] = None
    created_at: datetime
    updated_at: datetime

class CreateDistributionRuleRequest(BaseModel):
    rule_date: date
    channel_receptions: List[ChannelReceptionCreate]
    distribution_rules: List[DistributionRuleCreate]

class CopyDistributionRuleRequest(BaseModel):
    source_date: date
    target_date: date

class GroupDistributionRatioBase(BaseModel):
    rule_date: date
    channel_type: str
    group_name: str
    distribution_ratio: int

class GroupDistributionRatioCreate(GroupDistributionRatioBase):
    pass

class GroupDistributionRatioUpdate(BaseModel):
    distribution_ratio: int

class GroupDistributionRatioResponse(GroupDistributionRatioBase):
    id: int
    created_at: datetime
    updated_at: datetime






# 批量操作数据模型
class BatchUpdateDistributionRuleRequest(BaseModel):
    """批量修改分发规则请求模型"""
    ids: List[int]
    update_data: Dict[str, Any]  # 要修改的字段和值

class BatchDeleteDistributionRuleRequest(BaseModel):
    """批量删除分发规则请求模型"""
    ids: List[int]

@router.get("/api/distribution/rules/{rule_id}")
def get_distribution_rule(rule_id: str, db: Session = Depends(get_db)):
    """获取单个分发规则详情"""
    rule = db.query(DistributionRule).filter(
        DistributionRule.id == rule_id,
        DistributionRule.is_deleted == False
    ).first()
    if not rule:
        raise HTTPException(status_code=404, detail="分发规则不存在")
    return rule

@router.post("/api/distribution/rules", response_model=None)
def create_distribution_rules(
    request: CreateDistributionRuleRequest,
    db: Session = Depends(get_db)
):
    """创建分发规则"""
    # 不再删除已有规则，而是直接追加新规则
    
    # 创建渠道接待数记录
    channel_receptions = []
    for cr_data in request.channel_receptions:
        # 检查该日期和渠道类型是否已有接待数记录
        existing_reception = db.query(ChannelReception).filter(
            ChannelReception.rule_date == cr_data.rule_date,
            ChannelReception.channel_type == cr_data.channel_type
        ).first()
        
        if existing_reception:
            # 如果已存在，则更新接待数
            existing_reception.expected_reception = cr_data.expected_reception
            existing_reception.expected_free_reception = cr_data.expected_free_reception
            existing_reception.expected_paid_reception = cr_data.expected_paid_reception
            channel_receptions.append(existing_reception)
        else:
            # 如果不存在，则创建新记录
            channel_reception = ChannelReception(
                ID=str(uuid.uuid4()),
                rule_date=cr_data.rule_date,
                channel_type=cr_data.channel_type,
                expected_reception=cr_data.expected_reception,
                expected_free_reception=cr_data.expected_free_reception,
                expected_paid_reception=cr_data.expected_paid_reception
            )
            db.add(channel_reception)
            channel_receptions.append(channel_reception)
    
    # 创建分发规则记录
    rules = []
    for rule_data in request.distribution_rules:
        # 计算在岗人数（按分组统计）
        on_duty_count = sum(1 for r in request.distribution_rules if r.group_name == rule_data.group_name and r.channel_type == rule_data.channel_type)
        
        # 构建时间范围字符串
        time_range = f"{rule_data.time_range_start.strftime('%H:%M')}-{rule_data.time_range_end.strftime('%H:%M')}"

        # 构建完整的时间戳（结合日期和时间）
        time_range_start_datetime = datetime.combine(rule_data.rule_date, rule_data.time_range_start)
        time_range_end_datetime = datetime.combine(rule_data.rule_date, rule_data.time_range_end)

        rule = DistributionRule(
            date=rule_data.rule_date,
            channel_type=rule_data.channel_type,
            group_name=rule_data.group_name,
            leader=rule_data.leader,
            member=rule_data.member,
            on_duty_count=on_duty_count,
            shift=rule_data.shift,
            expected_total=rule_data.expected_reception,
            expected_free=rule_data.expected_free_reception,
            expected_paid=rule_data.expected_paid_reception,
            store=rule_data.store,
            time_range=time_range,
            time_range_start=time_range_start_datetime,
            time_range_end=time_range_end_datetime,
            remark=rule_data.remarks
        )
        db.add(rule)
        rules.append(rule)
    
    db.commit()
    
    # 刷新对象以获取数据库生成的值
    for rule in rules:
        db.refresh(rule)
    
    return {
        "status": "success",
        "message": f"成功创建 {request.rule_date} 的分发规则",
        "rules_count": len(rules),
        "channel_receptions_count": len(channel_receptions)
    }



@router.put("/api/distribution/rules/{rule_id}")
def update_distribution_rule(
    rule_id: str,
    rule_data: DistributionRuleUpdate,
    db: Session = Depends(get_db)
):
    """更新分发规则"""
    rule = db.query(DistributionRule).filter(
        DistributionRule.id == rule_id,
        DistributionRule.is_deleted == False
    ).first()
    if not rule:
        raise HTTPException(status_code=404, detail="分发规则不存在")

    # 记录原字段值用于日志
    old_values = {}
    for field in ['expected_total', 'expected_free', 'expected_paid']:
        old_values[field] = getattr(rule, field, None)

    # 创建字段映射关系
    field_mapping = {
        'expected_reception': 'expected_total',
        'expected_free_reception': 'expected_free',
        'expected_paid_reception': 'expected_paid',
        'time_range_start': None,  # 不直接映射，需要特殊处理
        'time_range_end': None,  # 不直接映射，需要特殊处理
        'remarks': 'remark'
    }
    # 获取规则数据字典
    data_dict = rule_data.model_dump(exclude_unset=True)  # 仅包含实际传入的字段

    print(f"接收到的数据字典: {data_dict}")

    # 处理时间范围字段
    if 'time_range_start' in data_dict and 'time_range_end' in data_dict and 'shift' in data_dict :

        time_range_start = data_dict.get('time_range_start')
        time_range_end = data_dict.get('time_range_end')
        shift = data_dict.get('shift')
        if time_range_start and time_range_end and shift:
            # 构建时间范围字符串
            time_range = f"{datetime.fromisoformat(time_range_start).strftime('%H:%M')}-{datetime.fromisoformat(time_range_end).strftime('%H:%M')}"
            setattr(rule, 'time_range', time_range)
            setattr(rule, 'time_range_start', time_range_start)
            setattr(rule, 'time_range_end', time_range_end)
    # 更新规则字段
    updated_fields = []
    for key, value in data_dict.items():
        # 检查是否需要特殊映射
        if key in field_mapping:
            if field_mapping[key] is not None:  # 如果有映射字段
                db_field = field_mapping[key]
                old_value = getattr(rule, db_field, None)
                setattr(rule, db_field, value)
                updated_fields.append(f"{db_field}: {old_value} -> {value}")
            # 不处理那些映射为None的字段，因为它们需要特殊处理
        else:
            # 没有映射的字段直接设置，但排除特殊处理的字段
            if key not in ['time_range_start', 'time_range_end']:
                old_value = getattr(rule, key, None)
                setattr(rule, key, value)
                updated_fields.append(f"{key}: {old_value} -> {value}")

    # 重新计算在岗人数
    rules_in_group = db.query(DistributionRule).filter(
        DistributionRule.date == rule.date,
        DistributionRule.channel_type == rule.channel_type,
        DistributionRule.group_name == rule.group_name,
        DistributionRule.is_deleted == False
    ).all()

    staff_count = len(rules_in_group)
    for r in rules_in_group:
        r.on_duty_count = staff_count

    # 记录日志
    print(f"更新规则 ID: {rule_id}, 修改字段: {', '.join(updated_fields)}")

    db.commit()
    db.refresh(rule)

    return rule


@router.delete("/api/distribution/rules/{rule_id}")
def delete_distribution_rule(
    rule_id: str,
    db: Session = Depends(get_db)
):
    """删除分发规则（软删除）"""
    rule = db.query(DistributionRule).filter(
        DistributionRule.id == rule_id,
        DistributionRule.is_deleted == False
    ).first()
    if not rule:
        raise HTTPException(status_code=404, detail="分发规则不存在")
    
    # 获取规则信息，用于更新同组规则的在岗人数
    rule_date = rule.date
    channel_type = rule.channel_type
    group_name = rule.group_name
    
    # 软删除规则
    rule.is_deleted = True
    
    # 更新同组规则的在岗人数（只计算未删除的规则）
    rules_in_group = db.query(DistributionRule).filter(
        DistributionRule.date == rule_date,
        DistributionRule.channel_type == channel_type,
        DistributionRule.group_name == group_name,
        DistributionRule.is_deleted == False
    ).all()
    
    staff_count = len(rules_in_group)
    for r in rules_in_group:
        r.on_duty_count = staff_count
    
    db.commit()
    
    return {"message": "分发规则已删除"}

@router.put("/api/distribution/plrules")
def batch_update_distribution_rules(
    request: BatchUpdateDistributionRuleRequest,
    db: Session = Depends(get_db)
):
    """批量修改分发规则"""
    if not request.ids:
        raise HTTPException(status_code=400, detail="未提供要修改的ID列表")
    
    # 查询要修改的规则（只查询未删除的）
    rules = db.query(DistributionRule).filter(
        DistributionRule.id.in_(request.ids),
        DistributionRule.is_deleted == False
    ).all()
    
    if not rules:
        raise HTTPException(status_code=404, detail="未找到要修改的分发规则")
    
    # 创建字段映射关系
    field_mapping = {
        'expected_reception': 'expected_total',
        'expected_free_reception': 'expected_free',
        'expected_paid_reception': 'expected_paid',
        'time_range_start': None,  # 需要特殊处理
        'time_range_end': None,    # 需要特殊处理
        'remarks': 'remark'
    }
    
    updated_count = 0
    affected_groups = set()
    
    for rule in rules:
        # 记录受影响的分组，用于后续更新在岗人数
        affected_groups.add((rule.date, rule.channel_type, rule.group_name))
        
        # 处理时间范围字段
        if 'time_range_start' in request.update_data and 'time_range_end' in request.update_data and 'shift' in request.update_data:
            time_range_start = request.update_data.get('time_range_start')
            time_range_end = request.update_data.get('time_range_end')
            shift = request.update_data.get('shift')
            
            if time_range_start and time_range_end and shift:
                # 构建时间范围字符串
                time_range = f"{datetime.fromisoformat(time_range_start).strftime('%H:%M')}-{datetime.fromisoformat(time_range_end).strftime('%H:%M')}"
                setattr(rule, 'time_range', time_range)
                setattr(rule, 'time_range_start', time_range_start)
                setattr(rule, 'time_range_end', time_range_end)
        
        # 更新其他字段
        for key, value in request.update_data.items():
            if key in field_mapping:
                if field_mapping[key] is not None:  # 如果有映射字段
                    db_field = field_mapping[key]
                    setattr(rule, db_field, value)
            else:
                # 没有映射的字段直接设置，但排除特殊处理的字段
                if key not in ['time_range_start', 'time_range_end']:
                    setattr(rule, key, value)
        
        # 更新时间戳
        rule.updated_at = datetime.now()
        updated_count += 1
    
    # 重新计算受影响分组的在岗人数
    for rule_date, channel_type, group_name in affected_groups:
        rules_in_group = db.query(DistributionRule).filter(
            DistributionRule.date == rule_date,
            DistributionRule.channel_type == channel_type,
            DistributionRule.group_name == group_name,
            DistributionRule.is_deleted == False
        ).all()
        
        staff_count = len(rules_in_group)
        for r in rules_in_group:
            r.on_duty_count = staff_count
    
    db.commit()
    
    return {
        "message": f"成功批量修改 {updated_count} 条分发规则",
        "updated_count": updated_count,
        "updated_ids": [rule.id for rule in rules]
    }

@router.delete("/api/distribution/plrules")
def batch_delete_distribution_rules(
    request: BatchDeleteDistributionRuleRequest,
    db: Session = Depends(get_db)
):
    """批量删除分发规则（软删除）"""
    if not request.ids:
        raise HTTPException(status_code=400, detail="未提供要删除的ID列表")
    
    # 查询要删除的规则（只查询未删除的）
    rules = db.query(DistributionRule).filter(
        DistributionRule.id.in_(request.ids),
        DistributionRule.is_deleted == False
    ).all()
    
    if not rules:
        raise HTTPException(status_code=404, detail="未找到要删除的分发规则")
    
    affected_groups = set()
    deleted_count = 0
    
    # 软删除所有选中的规则
    for rule in rules:
        # 记录受影响的分组，用于后续更新在岗人数
        affected_groups.add((rule.date, rule.channel_type, rule.group_name))
        
        # 软删除
        rule.is_deleted = True
        deleted_count += 1
    
    # 重新计算受影响分组的在岗人数（只计算未删除的规则）
    for rule_date, channel_type, group_name in affected_groups:
        rules_in_group = db.query(DistributionRule).filter(
            DistributionRule.date == rule_date,
            DistributionRule.channel_type == channel_type,
            DistributionRule.group_name == group_name,
            DistributionRule.is_deleted == False
        ).all()
        
        staff_count = len(rules_in_group)
        for r in rules_in_group:
            r.on_duty_count = staff_count
    
    db.commit()
    
    return {
        "message": f"成功批量删除 {deleted_count} 条分发规则",
        "deleted_count": deleted_count,
        "deleted_ids": [rule.id for rule in rules]
    }

@router.post("/api/distribution/copy",summary="复制分发规则到其他日期")
def copy_distribution_rules(
    request: CopyDistributionRuleRequest,
    db: Session = Depends(get_db)
):
   
    """复制分发规则到其他日期"""
    # 检查源日期是否有规则
    source_rules = db.query(DistributionRule).filter(
        DistributionRule.date == request.source_date,
        DistributionRule.is_deleted == False
    ).all()
    
    if not source_rules:
        raise HTTPException(status_code=404, detail="源日期没有分发规则")
    
    # 检查目标日期是否已有规则
    target_rules = db.query(DistributionRule).filter(
        DistributionRule.date == request.target_date,
        DistributionRule.is_deleted == False
    ).first()
    
    if target_rules:
        # 如果已存在，先删除旧规则
        db.query(DistributionRule).filter(
            DistributionRule.date == request.target_date
        ).delete()
        
        db.query(ChannelReception).filter(
            ChannelReception.rule_date == request.target_date
        ).delete()
    
    # 复制渠道接待数
    source_channel_receptions = db.query(ChannelReception).filter(
        ChannelReception.rule_date == request.source_date
    ).all()
    
    for scr in source_channel_receptions:
        new_cr = ChannelReception(
            ID=str(uuid.uuid4()),
            rule_date=request.target_date,
            channel_type=scr.channel_type,
            expected_reception=scr.expected_reception,
            expected_free_reception=scr.expected_free_reception,
            expected_paid_reception=scr.expected_paid_reception
        )
        db.add(new_cr)
    
    # 复制分发规则
    new_rules = []
    for sr in source_rules:
        # 从源规则的时间字段中提取时间部分，然后与目标日期组合
        source_start_time = sr.time_range_start.time()
        source_end_time = sr.time_range_end.time()

        # 构建目标日期的完整时间戳
        target_start_datetime = datetime.combine(request.target_date, source_start_time)
        target_end_datetime = datetime.combine(request.target_date, source_end_time)

        new_rule = DistributionRule(
            date=request.target_date,
            channel_type=sr.channel_type,
            group_name=sr.group_name,
            leader=sr.leader,
            member=sr.member,
            on_duty_count=sr.on_duty_count,
            shift=sr.shift,
            expected_total=sr.expected_total,
            expected_free=sr.expected_free,
            expected_paid=sr.expected_paid,
            store=sr.store,
            time_range=sr.time_range,  # 保持原有的时间范围字符串
            time_range_start=target_start_datetime,  # 设置目标日期的开始时间
            time_range_end=target_end_datetime,      # 设置目标日期的结束时间
            remark=sr.remark
        )
        db.add(new_rule)
        new_rules.append(new_rule)
    
    db.commit()
    
    return {"message": f"已成功将 {request.source_date} 的分发规则复制到 {request.target_date}"}

@router.get("/api/distribution/channel-receptions", response_model=List[ChannelReceptionResponse])
def get_channel_receptions(
    rule_date: date = Query(None),
    channel_type: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """获取渠道接待数列表"""
    try:
        query = db.query(ChannelReception)
        
        if rule_date:
            query = query.filter(ChannelReception.rule_date == rule_date)
        
        if channel_type:
            query = query.filter(ChannelReception.channel_type == channel_type)
        
        channel_receptions = query.order_by(ChannelReception.channel_type).all()
        return channel_receptions
    except Exception as e:
        print(f"获取渠道接待数失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取渠道接待数失败: {str(e)}")

@router.put("/api/distribution/update-actual-reception/{rule_id}")
def update_actual_reception(
    rule_id: str,
    actual_reception: int = Query(..., description="实际接待总数"),
    actual_free_reception: int = Query(..., description="实际免费接待数"),
    actual_paid_reception: int = Query(..., description="实际付费接待数"),
    db: Session = Depends(get_db)
):
    """更新实际接待数"""
    try:
        rule = db.query(DistributionRule).filter(
            DistributionRule.id == rule_id,
            DistributionRule.is_deleted == False
        ).first()
        if not rule:
            raise HTTPException(status_code=404, detail="分发规则不存在")
        
        # 更新对应字段
        rule.actual_total = actual_reception
        rule.actual_free = actual_free_reception
        rule.actual_paid = actual_paid_reception
        
        # 更新时间戳
        rule.updated_at = datetime.now()
        
        db.commit()
        db.refresh(rule)
        
        print(f"更新规则ID {rule_id} 的实际接待数: 总数={actual_reception}, 免费={actual_free_reception}, 付费={actual_paid_reception}")
        
        return {"message": "实际接待数已更新", "data": {
            "rule_id": rule_id,
            "actual_total": rule.actual_total,
            "actual_free": rule.actual_free, 
            "actual_paid": rule.actual_paid
        }}
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        error_msg = f"更新实际接待数失败: {str(e)}"
        print(f"错误: {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)

# 获取预设数据的API
@router.get("/api/distribution/presets/groups")
def get_preset_groups(db: Session = Depends(get_db)):
    """获取预设的分组数据"""
    # 这里需要从表单预设中获取分组数据
    # 暂时返回模拟数据，后续需要对接实际的预设数据API
    return [
        {"id": "1", "name": "销售一组"},
        {"id": "2", "name": "销售二组"},
        {"id": "3", "name": "销售三组"},
        {"id": "4", "name": "新媒体组"}
    ]

@router.get("/api/distribution/presets/leaders")
def get_preset_leaders(group_id: Optional[str] = Query(None), db: Session = Depends(get_db)):
    """获取预设的负责人数据"""
    # 这里需要从表单预设中获取负责人数据
    # 暂时返回模拟数据，后续需要对接实际的预设数据API
    leaders = {
        "1": [{"id": "101", "name": "张三"}],
        "2": [{"id": "102", "name": "李四"}],
        "3": [{"id": "103", "name": "王五"}],
        "4": [{"id": "104", "name": "赵六"}]
    }
    
    if group_id and group_id in leaders:
        return leaders[group_id]
    
    # 如果没有指定分组或分组不存在，返回所有负责人
    all_leaders = []
    for group_leaders in leaders.values():
        all_leaders.extend(group_leaders)
    
    return all_leaders

@router.get("/api/distribution/presets/members")
def get_preset_members(group_id: Optional[str] = Query(None), db: Session = Depends(get_db)):
    """获取预设的成员数据"""
    # 这里需要从表单预设中获取成员数据
    # 暂时返回模拟数据，后续需要对接实际的预设数据API
    members = {
        "1": [
            {"id": "1001", "name": "成员A"},
            {"id": "1002", "name": "成员B"},
            {"id": "1003", "name": "成员C"}
        ],
        "2": [
            {"id": "1004", "name": "成员D"},
            {"id": "1005", "name": "成员E"},
            {"id": "1006", "name": "成员F"}
        ],
        "3": [
            {"id": "1007", "name": "成员G"},
            {"id": "1008", "name": "成员H"},
            {"id": "1009", "name": "成员I"}
        ],
        "4": [
            {"id": "1010", "name": "成员J"},
            {"id": "1011", "name": "成员K"},
            {"id": "1012", "name": "成员L"}
        ]
    }
    
    if group_id and group_id in members:
        return members[group_id]
    
    # 如果没有指定分组或分组不存在，返回所有成员
    all_members = []
    for group_members in members.values():
        all_members.extend(group_members)
    
    return all_members

@router.get("/api/distribution/presets/stores")
def get_preset_stores(db: Session = Depends(get_db)):
    """获取预设的店铺数据"""
    # 这里需要从表单预设中获取店铺数据
    # 暂时返回模拟数据，后续需要对接实际的预设数据API
    return [
        {"id": "s1", "name": "店铺A"},
        {"id": "s2", "name": "店铺B"},
        {"id": "s3", "name": "店铺C"},
        {"id": "s4", "name": "店铺D"}
    ]

@router.get("/api/presets/sales")
def get_preset_sales(db: Session = Depends(get_db)):
    """获取预设的销售组数据，包括负责人和成员信息"""
    # 这里返回模拟数据，后续需要对接实际的销售组数据API
    return {
        "sales": [
            {
                "id": "1",
                "name": "销售一组",
                "leader": "张三",
                "members": "李四,王五,赵六,钱七"
            },
            {
                "id": "2",
                "name": "销售二组",
                "leader": "刘备",
                "members": "关羽,张飞,诸葛亮,赵云"
            },
            {
                "id": "3",
                "name": "销售三组",
                "leader": "曹操",
                "members": "典韦,许褚,郭嘉,荀彧"
            },
            {
                "id": "4",
                "name": "新媒体组",
                "leader": "孙权",
                "members": "周瑜,鲁肃,黄盖,甘宁"
            }
        ]
    }

@router.get("/api/presets/shops/detail")
def get_preset_shops_detail(db: Session = Depends(get_db)):
    """获取预设的店铺详细数据"""
    # 这里返回模拟数据，后续需要对接实际的店铺数据API
    return [
        {"id": "s1", "name": "天猫旗舰店", "platform": "天猫", "category": "电商"},
        {"id": "s2", "name": "京东自营店", "platform": "京东", "category": "电商"},
        {"id": "s3", "name": "抖音小店", "platform": "抖音", "category": "新媒体"},
        {"id": "s4", "name": "快手小店", "platform": "快手", "category": "新媒体"}
    ]

@router.get("/api/distribution/group-ratios", response_model=List[GroupDistributionRatioResponse])
def get_group_distribution_ratios(
    rule_date: date = Query(None),
    channel_type: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """获取分组分配数量列表"""
    try:
        query = db.query(GroupDistributionRatio)
        
        if rule_date:
            query = query.filter(GroupDistributionRatio.rule_date == rule_date)
        
        if channel_type:
            query = query.filter(GroupDistributionRatio.channel_type == channel_type)
        
        ratios = query.order_by(GroupDistributionRatio.channel_type, GroupDistributionRatio.group_name).all()
        return ratios
    except Exception as e:
        print(f"获取分组分配数量失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取分组分配数量失败: {str(e)}")

@router.post("/api/distribution/group-ratios", response_model=GroupDistributionRatioResponse)
def create_group_distribution_ratio(
    ratio_data: GroupDistributionRatioCreate,
    db: Session = Depends(get_db)
):
    """创建或更新分组分配数量"""
    try:
        # 检查是否已存在相同日期、渠道类型和分组名称的记录
        existing_ratio = db.query(GroupDistributionRatio).filter(
            GroupDistributionRatio.rule_date == ratio_data.rule_date,
            GroupDistributionRatio.channel_type == ratio_data.channel_type,
            GroupDistributionRatio.group_name == ratio_data.group_name
        ).first()
        
        if existing_ratio:
            # 如果已存在，则更新分配数量
            existing_ratio.distribution_ratio = ratio_data.distribution_ratio
            existing_ratio.updated_at = datetime.now()
            db.commit()
            db.refresh(existing_ratio)
            return existing_ratio
        else:
            # 如果不存在，则创建新记录
            new_ratio = GroupDistributionRatio(
                rule_date=ratio_data.rule_date,
                channel_type=ratio_data.channel_type,
                group_name=ratio_data.group_name,
                distribution_ratio=ratio_data.distribution_ratio
            )
            db.add(new_ratio)
            db.commit()
            db.refresh(new_ratio)
            return new_ratio
    except Exception as e:
        print(f"创建或更新分组分配数量失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建或更新分组分配数量失败: {str(e)}")

@router.put("/api/distribution/group-ratios/{ratio_id}", response_model=GroupDistributionRatioResponse)
def update_group_distribution_ratio(
    ratio_id: int,
    ratio_data: GroupDistributionRatioUpdate,
    db: Session = Depends(get_db)
):
    """更新分组分配数量"""
    try:
        ratio = db.query(GroupDistributionRatio).filter(GroupDistributionRatio.id == ratio_id).first()
        if not ratio:
            raise HTTPException(status_code=404, detail="分组分配数量记录不存在")
        
        ratio.distribution_ratio = ratio_data.distribution_ratio
        ratio.updated_at = datetime.now()
        
        db.commit()
        db.refresh(ratio)
        
        return ratio
    except HTTPException:
        raise
    except Exception as e:
        print(f"更新分组分配数量失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新分组分配数量失败: {str(e)}")

@router.delete("/api/distribution/group-ratios")
def delete_group_distribution_ratios(
    rule_date: date = Query(...),
    db: Session = Depends(get_db)
):
    """删除指定日期的分组分配数量"""
    try:
        # 查询指定日期的分组分配数量记录
        ratios = db.query(GroupDistributionRatio).filter(
            GroupDistributionRatio.rule_date == rule_date
        ).all()
        
        if not ratios:
            return {"message": f"未找到 {rule_date} 的分组分配数量记录"}
        
        # 删除记录
        count = db.query(GroupDistributionRatio).filter(
            GroupDistributionRatio.rule_date == rule_date
        ).delete()
        
        db.commit()
        
        return {"message": f"成功删除 {count} 条分组分配数量记录"}
    except Exception as e:
        print(f"删除分组分配数量失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除分组分配数量失败: {str(e)}")

# 添加更新渠道接待数量的API路由
class UpdateChannelReceptionRequest(BaseModel):
    expected_reception: Optional[int] = None
    expected_free_reception: Optional[int] = None
    expected_paid_reception: Optional[int] = None

@router.put("/api/distribution/reception/{rule_date}/{channel_type}")
def update_channel_reception(
    rule_date: str,
    channel_type: str,
    request: UpdateChannelReceptionRequest,
    db: Session = Depends(get_db)
):
    """更新渠道预计接待数量"""
    try:
        # 将日期字符串转换为date对象
        date_obj = date.fromisoformat(rule_date)
        
        # 转换中文渠道名称为数据库中的格式
        channel_mapping = {
            "电商渠道": "电商渠道",
            "新媒体渠道": "新媒体渠道",
            "公海渠道": "公海渠道"
        }
        
        actual_channel_type = channel_mapping.get(channel_type, channel_type)
        
        # 查询指定日期和渠道类型的接待数记录
        channel_reception = db.query(ChannelReception).filter(
            ChannelReception.rule_date == date_obj,
            ChannelReception.channel_type == actual_channel_type
        ).first()
        
        # 如果记录不存在，则创建新记录
        if not channel_reception:
            channel_reception = ChannelReception(
                ID=str(uuid.uuid4()),
                rule_date=date_obj,
                channel_type=actual_channel_type,
                expected_reception=0,
                expected_free_reception=0,
                expected_paid_reception=0
            )
            db.add(channel_reception)
        
        # 更新记录字段，只更新请求中包含的字段
        update_data = request.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            if value is not None:
                setattr(channel_reception, key, value)
        
        channel_reception.updated_at = datetime.now()
        
        db.commit()
        db.refresh(channel_reception)
        
        return {
            "message": f"成功更新 {rule_date} {channel_type} 的预计接待数量",
            "data": channel_reception
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"日期格式错误: {str(e)}")
    except Exception as e:
        print(f"更新渠道预计接待数量失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新渠道预计接待数量失败: {str(e)}")

@router.delete("/api/distribution/reception/{rule_date}/{channel_type}")
def delete_channel_reception(
    rule_date: str,
    channel_type: str,
    db: Session = Depends(get_db)
):
    """删除渠道接待数记录"""
    try:
        # 将日期字符串转换为date对象
        date_obj = date.fromisoformat(rule_date)
        
        # 转换中文渠道名称为数据库中的格式
        channel_mapping = {
            "电商渠道": "电商渠道",
            "新媒体渠道": "新媒体渠道",
            "公海渠道": "公海渠道"
        }
        
        actual_channel_type = channel_mapping.get(channel_type, channel_type)
        
        # 查询并删除指定日期和渠道类型的接待数记录
        deleted_count = db.query(ChannelReception).filter(
            ChannelReception.rule_date == date_obj,
            ChannelReception.channel_type == actual_channel_type
        ).delete()
        
        db.commit()
        
        if deleted_count > 0:
            return {
                "message": f"成功删除 {rule_date} {channel_type} 的渠道接待数记录",
                "deleted_count": deleted_count
            }
        else:
            return {
                "message": f"未找到 {rule_date} {channel_type} 的渠道接待数记录",
                "deleted_count": 0
            }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"日期格式错误: {str(e)}")
    except Exception as e:
        print(f"删除渠道接待数记录失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除渠道接待数记录失败: {str(e)}")

# 分发队列相关模型
class DistributionQueueRequest(BaseModel):
    rule_date: date
    channel_type: str
    algorithm: str = "dynamic_weight"  # 默认为动态权重轮循法

class DistributionQueueItem(BaseModel):
    group_name: str
    leader: str
    member: str
    position: int  # 在队列中的位置
    time_slot: str  # 时间段

class DistributionQueueResponse(BaseModel):
    queue: List[DistributionQueueItem]
    algorithm: str
    total_items: int
    channel_type: str
    rule_date: date
    algorithm_description: str

# 分发队列API
@router.post("/api/distribution/queue", response_model=DistributionQueueResponse)
def generate_distribution_queue(
    request: DistributionQueueRequest,
    db: Session = Depends(get_db)
):
    """生成分发队列
    
    使用指定的算法根据分发规则生成队列。
    支持两种算法：
    - phased_quota: 交替配额轮循法
    - dynamic_weight: 动态权重轮循法
    """
    try:
        # 获取指定日期和渠道类型的规则
        rules = db.query(DistributionRule).filter(
            DistributionRule.date == request.rule_date,
            DistributionRule.channel_type == request.channel_type,
            DistributionRule.is_deleted == False
        ).all()
        
        if not rules:
            raise HTTPException(status_code=404, detail=f"未找到 {request.rule_date} 的分发规则")
        
        # 按组聚合规则
        groups = defaultdict(list)
        for rule in rules:
            groups[rule.group_name].append(rule)
        
        # 计算每个组的预计接待总数
        group_quotas = {}
        for group_name, group_rules in groups.items():
            total_expected = sum(rule.expected_total for rule in group_rules)
            group_quotas[group_name] = total_expected
        
        # 生成分发队列
        if request.algorithm.lower() == "phased_quota":
            queue, description = phased_quota_round_robin(groups, group_quotas)
        else:  # 默认使用动态权重轮循法
            queue, description = dynamic_weighted_round_robin(groups, group_quotas)
        
        # 构建响应
        return DistributionQueueResponse(
            queue=queue,
            algorithm=request.algorithm,
            total_items=len(queue),
            channel_type=request.channel_type,
            rule_date=request.rule_date,
            algorithm_description=description
        )
    except Exception as e:
        print(f"生成分发队列失败: {e}")
        raise HTTPException(status_code=500, detail=f"生成分发队列失败: {str(e)}")

def phased_quota_round_robin(groups, group_quotas):
    """交替配额轮循法（基于轮询算法优化）

    将总请求按固定阶段划分，在每个阶段内按初始配额比例分配。
    在组内使用轮询算法确保成员分配的公平性。
    """
    # 计算最大公约数用于确定阶段大小
    def gcd(a, b):
        while b:
            a, b = b, a % b
        return a

    def find_gcd_of_list(numbers):
        result = numbers[0]
        for num in numbers[1:]:
            result = gcd(result, num)
        return result

    # 计算配额最小单位（每阶段的总长度）
    quotas = list(group_quotas.values())
    if all(q == 0 for q in quotas):
        return [], "所有组的预计接待数都为0"

    # 过滤掉配额为0的组
    non_zero_quotas = [q for q in quotas if q > 0]
    if not non_zero_quotas:
        return [], "所有组的预计接待数都为0"

    # 找到配额的最大公约数
    quota_gcd = find_gcd_of_list(non_zero_quotas)

    # 计算每个组在一个阶段内应分配的次数
    phase_allocations = {}
    for group_name, quota in group_quotas.items():
        phase_allocations[group_name] = quota // quota_gcd if quota > 0 else 0

    # 初始化每个组的轮询索引
    group_member_indices = {}
    for group_name in groups.keys():
        group_member_indices[group_name] = 0

    # 构建阶段分配序列
    queue = []
    position = 1

    # 计算总共需要多少个阶段
    total_quota = sum(quotas)
    total_phases_needed = total_quota // quota_gcd if quota_gcd > 0 else 0

    # 生成完整队列
    for _ in range(total_phases_needed):
        for group_name, allocation in phase_allocations.items():
            if allocation == 0:
                continue

            # 获取组内成员
            group_members = groups[group_name]

            # 按时间段排序组内成员
            sorted_members = sorted(group_members, key=lambda x: x.time_range)

            # 使用轮询算法分配给组内成员
            for _ in range(allocation):
                # 使用轮询索引选择组内成员
                current_index = group_member_indices[group_name]
                rule = sorted_members[current_index]

                # 更新轮询索引（轮询到下一个成员）
                group_member_indices[group_name] = (current_index + 1) % len(sorted_members)

                queue.append(DistributionQueueItem(
                    group_name=group_name,
                    leader=rule.leader,
                    member=rule.member,
                    position=position,
                    time_slot=rule.time_range
                ))
                position += 1

    description = "交替配额轮循法（轮询优化版）：将总请求按固定阶段划分，在每个阶段内按初始配额比例分配。组内使用A→B→C→A轮询算法确保成员分配公平性。"
    return queue, description

def dynamic_weighted_round_robin(groups, group_quotas):
    """动态权重轮循法（基于轮询算法优化）

    根据节点的剩余配额动态计算权重，每次分配时选择当前剩余比例最高的节点。
    在组内使用轮询算法确保成员分配的公平性。
    """
    if not group_quotas or all(quota == 0 for quota in group_quotas.values()):
        return [], "所有组的预计接待数都为0"

    # 初始化每个组的成员和剩余配额
    group_members = {}
    remaining_quotas = {}
    group_member_indices = {}  # 记录每个组当前轮询到的成员索引

    for group_name, quota in group_quotas.items():
        if quota > 0:
            # 按时间段排序组内成员
            group_members[group_name] = sorted(groups[group_name].copy(), key=lambda x: x.time_range)
            remaining_quotas[group_name] = quota
            group_member_indices[group_name] = 0  # 初始化轮询索引

    # 初始化优先队列
    # 使用负数是因为heapq是最小堆，我们需要最大优先级
    priority_queue = []
    total_quota = sum(group_quotas.values())

    for group_name, quota in remaining_quotas.items():
        # 剩余配额比例作为优先级
        priority = -(quota / total_quota)
        heapq.heappush(priority_queue, (priority, group_name))

    # 生成分发队列
    queue = []
    position = 1

    while priority_queue:
        # 获取当前优先级最高的组
        _, group_name = heapq.heappop(priority_queue)

        # 检查该组是否还有剩余配额
        if remaining_quotas[group_name] <= 0:
            continue

        # 从组中使用轮询方式选择成员
        members = group_members[group_name]
        current_index = group_member_indices[group_name]
        rule = members[current_index]

        # 更新该组的轮询索引（轮询到下一个成员）
        group_member_indices[group_name] = (current_index + 1) % len(members)

        # 添加到队列
        queue.append(DistributionQueueItem(
            group_name=group_name,
            leader=rule.leader,
            member=rule.member,
            position=position,
            time_slot=rule.time_range
        ))
        position += 1

        # 更新剩余配额
        remaining_quotas[group_name] -= 1

        # 如果还有剩余配额，重新计算优先级并加回队列
        if remaining_quotas[group_name] > 0:
            # 重新计算总配额
            current_total = sum(remaining_quotas.values())
            # 重新计算优先级
            priority = -(remaining_quotas[group_name] / current_total)
            heapq.heappush(priority_queue, (priority, group_name))

    description = "动态权重轮循法（轮询优化版）：根据节点的剩余配额动态计算权重，每次分配时选择当前剩余比例最高的节点。组内使用A→B→C→A轮询算法确保成员分配公平性。"
    return queue, description

# 获取分发队列的API
@router.get("/api/distribution/queue", response_model=DistributionQueueResponse)
def get_distribution_queue(
    rule_date: date = Query(..., description="规则日期"),
    channel_type: str = Query(..., description="渠道类型"),
    algorithm: str = Query("dynamic_weight", description="算法类型，可选值：phased_quota, dynamic_weight"),
    db: Session = Depends(get_db)
):
    """获取分发队列"""
    request = DistributionQueueRequest(
        rule_date=rule_date,
        channel_type=channel_type,
        algorithm=algorithm
    )
    return generate_distribution_queue(request, db)

class SaveDistributionQueueRequest(BaseModel):
    queue_date: date
    channel_type: str
    free_queue: List[Dict[str, Any]]
    paid_queue: List[Dict[str, Any]]

@router.post("/api/distribution/queue/save", response_model=None)
def save_distribution_queue(
    request: Request,
    req_data: SaveDistributionQueueRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):

    """保存分发队列数据到数据库"""
    try:
        # 获取当前用户
        current_user = get_current_user(request, db)

        if not current_user:
            raise HTTPException(status_code=401, detail="用户未登录")
       # return {req_data.queue_date,req_data.channel_type}
        # 1. 获取现有的已分发队列项
        existing_distributed_items = {}
        existing_items = db.query(DistributionQueue).filter(
            DistributionQueue.queue_date == req_data.queue_date,
            DistributionQueue.channel_type == req_data.channel_type,
            DistributionQueue.status == "distributed"
        ).all()

        # 创建一个查找字典，键为队列类型+成员+位置，值为已分发的队列项
        for item in existing_items:
            key = f"{item.queue_type}_{item.member}_{item.position}"
            existing_distributed_items[key] = {
                "status": item.status,
                "distributed_at": item.distributed_at,
                "distributed_by": item.distributed_by,
                "remark": item.remark
            }
        
        # 2. 删除该日期和渠道类型的所有队列项
        db.query(DistributionQueue).filter(
            DistributionQueue.queue_date == req_data.queue_date,
            DistributionQueue.channel_type == req_data.channel_type
        ).delete()
        
        # 3. 重新创建队列项，对于在步骤1中识别为已分发状态的项，恢复其状态
        # 保存免费队列
        for idx, item in enumerate(req_data.free_queue):

            position = item.get("position") or (idx + 1)
            member = item.get("member", "")
            
            # 检查此项是否为已分发项
            key = f"free_{member}_{position}"
            distributed_info = existing_distributed_items.get(key)
            
            # 如果找到匹配的已分发项，使用它的状态和分发信息
            if distributed_info:
                status = "distributed"
                distributed_at = distributed_info["distributed_at"]
                distributed_by = distributed_info["distributed_by"]
                remark = distributed_info.get("remark", "")
            else:
                # 使用默认值或前端传入的值
                status = item.get("status", "pending")
                distributed_at = None
                distributed_by = None
                remark = item.get("remark", "")
                
                # 如果前端标记为已分发但在数据库中没有找到，尝试使用前端信息
                if status == "distributed":
                    try:
                        distributed_at_str = item.get("distributed_at")
                        if distributed_at_str and isinstance(distributed_at_str, str):
                            distributed_at = datetime.fromisoformat(distributed_at_str.replace('Z', '+00:00'))
                        distributed_by = item.get("distributed_by")
                    except Exception as e:
                        print(f"处理前端分发信息失败: {e}")

            nowtime = datetime.now()
            today = date.today()
            time_slot = item.get("time_slot", "")
            time_slot_parts_beg,time_slot_parts_end = time_slot.split("-")
            time_slot_parts_beg = time_slot_parts_beg+":00"
            time_slot_parts_end = time_slot_parts_end+":00"

            time_obj_beg = datetime.strptime(time_slot_parts_beg, "%H:%M:%S").time()
            time_obj_end = datetime.strptime(time_slot_parts_end, "%H:%M:%S").time()
            combined_beg = datetime.combine(today, time_obj_beg)
            combined_end = datetime.combine(today, time_obj_end)

            queue_item = DistributionQueue(
                queue_date=req_data.queue_date,
                channel_type=req_data.channel_type,
                queue_type="free",
                position=position,
                group_name=item.get("group_name", ""),
                leader=item.get("leader", ""),
                member=member,
                time_slot=time_slot,
                status=status,
                distributed_at=distributed_at,
                distributed_by=distributed_by,
                remark=remark,
                created_at=nowtime,
                updated_at=nowtime,
                time_slot_beg=combined_beg,
                time_slot_end=combined_end
            )
            db.add(queue_item)
        
        # 保存付费队列，逻辑同免费队列
        for idx, item in enumerate(req_data.paid_queue):
            position = item.get("position") or (idx + 1)
            member = item.get("member", "")
            
            # 检查此项是否为已分发项
            key = f"paid_{member}_{position}"
            distributed_info = existing_distributed_items.get(key)
            
            # 如果找到匹配的已分发项，使用它的状态和分发信息
            if distributed_info:
                status = "distributed"
                distributed_at = distributed_info["distributed_at"]
                distributed_by = distributed_info["distributed_by"]
                remark = distributed_info.get("remark", "")
            else:
                # 使用默认值或前端传入的值
                status = item.get("status", "pending")
                distributed_at = None
                distributed_by = None
                remark = item.get("remark", "")
                
                # 如果前端标记为已分发但在数据库中没有找到，尝试使用前端信息
                if status == "distributed":
                    try:
                        distributed_at_str = item.get("distributed_at")
                        if distributed_at_str and isinstance(distributed_at_str, str):
                            distributed_at = datetime.fromisoformat(distributed_at_str.replace('Z', '+00:00'))
                        distributed_by = item.get("distributed_by")
                    except Exception as e:
                        print(f"处理前端分发信息失败: {e}")

            nowtime = datetime.now()
            today = date.today()
            time_slot = item.get("time_slot", "")
            time_slot_parts_beg, time_slot_parts_end = time_slot.split("-")
            time_slot_parts_beg = time_slot_parts_beg + ":00"
            time_slot_parts_end = time_slot_parts_end + ":00"
            time_obj_beg = datetime.strptime(time_slot_parts_beg, "%H:%M:%S").time()
            time_obj_end = datetime.strptime(time_slot_parts_end, "%H:%M:%S").time()
            combined_beg = datetime.combine(today, time_obj_beg)
            combined_end = datetime.combine(today, time_obj_end)
            queue_item = DistributionQueue(
                queue_date=req_data.queue_date,
                channel_type=req_data.channel_type,
                queue_type="paid",
                position=position,
                group_name=item.get("group_name", ""),
                leader=item.get("leader", ""),
                member=member,
                time_slot=time_slot,
                status=status,
                distributed_at=distributed_at,
                distributed_by=distributed_by,
                remark=remark,
                created_at=nowtime,
                updated_at=nowtime,
                time_slot_beg=combined_beg,
                time_slot_end=combined_end,
            )
            db.add(queue_item)
        
        # 先提交保存所有队列项
        db.commit()
        
        # 4. 智能排序队列并更新position字段（保护已分发队列）
        # 分别处理免费队列和付费队列
        for queue_type in ["free", "paid"]:
            # 查询该队列类型的所有项
            queue_items = db.query(DistributionQueue).filter(
                DistributionQueue.queue_date == req_data.queue_date,
                DistributionQueue.channel_type == req_data.channel_type,
                DistributionQueue.queue_type == queue_type
            ).all()

            if queue_items:
                # 使用保护已分发队列的智能排序算法
                sorted_items = sort_queue_with_distributed_protection(queue_items, db, req_data.queue_date, req_data.channel_type)
                # 注意：新算法内部已经处理了position分配

                print(f"智能排序 {queue_type} 队列完成，共 {len(sorted_items)} 项（已分发队列保持不变）")
        
        # 提交排序更新
        db.commit()
        
        # 在后台任务中处理待分发线索
        background_tasks.add_task(process_pending_leads_background, db)
        
        return {"status": "success", "message": "队列保存成功，已应用智能排序算法（保护已分发队列），已开始处理待分发线索"}
    except Exception as e:
        db.rollback()
        print(f"保存分发队列失败: {e}")
        raise HTTPException(status_code=500, detail=f"保存分发队列失败: {str(e)}")

def sort_queue_by_shift_groups(queue_items: List, db: Session, queue_date: date, channel_type: str):
    """
    基于时间段交叉排序的队列处理逻辑

    处理流程：
    1. 提取每个队列项 time_slot 字段中的起始时间（time_slot_beg）
    2. 按照 time_slot_beg 进行升序排序（早的时间排在前面）
    3. 将相同 time_slot_beg 的元素归为一组
    4. 实现时间段交叉排序：不同时间段的队列交叉轮换排列
    5. 支持并行处理：后续时间段不需要等待前面时间段完成

    时间段交叉排序算法：
    - 例如有3个时间段：9:00-18:30, 13:30-23:59, 18:30-23:59
    - 排序结果：9:00-18:30 → 13:30-23:59 → 18:30-23:59 → 9:00-18:30 → ...
    - 循环继续直到所有时间段的队列都处理完毕
    - 分发系统会自动跳过不符合当前时间的队列项

    参数：
        queue_items: 队列项列表
        db: 数据库会话（保留兼容性，实际未使用）
        queue_date: 队列日期（保留兼容性，实际未使用）
        channel_type: 渠道类型（保留兼容性，实际未使用）

    返回：
        重新排序后的队列项列表
    """
    if not queue_items:
        return []
    
    print(f"开始按 time_slot 时间分组排序，共 {len(queue_items)} 个队列项")
    
    # 步骤1: 提取每个队列项的 time_slot_beg（起始时间）
    for item in queue_items:
        if hasattr(item, 'time_slot') and item.time_slot:
            try:
                # 从 time_slot 字段提取起始时间，格式如 "09:00-18:30"
                time_slot_beg = item.time_slot.split('-')[0].strip()
                # 转换为时间对象用于排序比较
                time_obj = datetime.strptime(time_slot_beg, "%H:%M").time()
                item.extracted_time_slot_beg = time_obj
                item.time_slot_beg_str = time_slot_beg
                print(f"队列项 {item.member}: time_slot='{item.time_slot}' -> time_slot_beg='{time_slot_beg}'")
            except Exception as e:
                # 如果解析失败，使用默认时间
                print(f"解析队列项 {item.member} 的 time_slot 失败: {e}，使用默认时间 09:00")
                item.extracted_time_slot_beg = time(9, 0)
                item.time_slot_beg_str = "09:00"
        else:
            # 如果没有 time_slot 字段，使用默认时间
            print(f"队列项 {item.member} 缺少 time_slot 字段，使用默认时间 09:00")
            item.extracted_time_slot_beg = time(9, 0)
            item.time_slot_beg_str = "09:00"
    
    # 步骤2: 按照 time_slot_beg 进行升序排序
    queue_items.sort(key=lambda x: x.extracted_time_slot_beg)
    print(f"按时间排序完成，时间顺序: {[item.time_slot_beg_str for item in queue_items]}")
    
    # 步骤3: 按照 time_slot_beg 相同的时间进行分组
    time_groups = {}
    for item in queue_items:
        time_key = item.time_slot_beg_str
        if time_key not in time_groups:
            time_groups[time_key] = []
        time_groups[time_key].append(item)

    print(f"时间分组结果: {[(time_key, len(items)) for time_key, items in time_groups.items()]}")

    # 步骤4: 实现时间段交叉排序算法
    sorted_queue = []
    position = 1

    # 按时间顺序获取所有时间段
    sorted_time_keys = sorted(time_groups.keys(), key=lambda x: datetime.strptime(x, "%H:%M").time())
    print(f"时间段排序: {sorted_time_keys}")

    # 为每个时间段准备队列项，按组名和ID排序
    time_slot_queues = {}
    for time_key in sorted_time_keys:
        time_items = time_groups[time_key]

        # 在当前时间分组内，按组名分类
        group_members = {}
        for item in time_items:
            group_name = item.group_name
            if group_name not in group_members:
                group_members[group_name] = []
            group_members[group_name].append(item)

        # 每个组内按ID排序，确保组内顺序稳定
        for group_name in group_members:
            group_members[group_name].sort(key=lambda x: x.id)

        # 将该时间段的所有队列项按组交叉排列
        time_slot_queue = []
        sorted_group_names = sorted(group_members.keys())
        max_rounds = max(len(members) for members in group_members.values()) if group_members else 0

        # 轮询分配：从每个组中依次取出一个元素
        for round_idx in range(max_rounds):
            for group_name in sorted_group_names:
                if round_idx < len(group_members[group_name]):
                    item = group_members[group_name][round_idx]
                    time_slot_queue.append(item)

        time_slot_queues[time_key] = time_slot_queue
        print(f"时间段 '{time_key}' 内部排序完成，共 {len(time_slot_queue)} 个项目")

    # 步骤5: 时间段交叉轮换排序
    # 计算最大队列长度，用于确定需要多少轮交叉
    max_queue_length = max(len(queue) for queue in time_slot_queues.values()) if time_slot_queues else 0
    print(f"开始时间段交叉排序，最大队列长度: {max_queue_length}")

    # 为每个时间段维护当前索引
    time_slot_indices = {time_key: 0 for time_key in sorted_time_keys}

    # 交叉轮换分配
    for round_idx in range(max_queue_length):
        for time_key in sorted_time_keys:
            current_index = time_slot_indices[time_key]
            time_slot_queue = time_slot_queues[time_key]

            # 如果当前时间段还有未分配的队列项
            if current_index < len(time_slot_queue):
                item = time_slot_queue[current_index]
                sorted_queue.append(item)
                time_slot_indices[time_key] += 1
                print(f"交叉分配: 轮次={round_idx + 1}, 时间段={time_key}, 成员={item.member}, 组={item.group_name}")

    print(f"时间段交叉排序完成，最终队列长度: {len(sorted_queue)}")
    return sorted_queue


def sort_queue_with_polling_algorithm(queue_items: List, db: Session, queue_date: date, channel_type: str):
    """
    轮询（Polling）排序算法

    解决问题：
    1. 避免同一人连续分发的问题
    2. 消除时间段聚集造成的分发不均
    3. 确保分发队列的公平性和可预测性

    轮询算法规则：
    1. 按时间段进行优先级排序（时间优先级）
    2. 在相同时间段内，按照 A→B→C→A→B→C 的顺序循环分配
    3. 跨时间段时，保持轮询顺序的连续性
    4. 确保相邻队列不能有相同的 time_slot_beg 或 member_name（交叉排列约束）

    处理流程：
    1. 按时间段分组队列项
    2. 在每个时间段内按成员进行轮询分配
    3. 跨时间段交叉轮换排序
    4. 返回轮询排序后的队列

    参数：
        queue_items: 队列项列表
        db: 数据库会话（保留兼容性）
        queue_date: 队列日期（保留兼容性）
        channel_type: 渠道类型（保留兼容性）

    返回：
        轮询排序后的队列项列表
    """
    if not queue_items:
        return []

    print(f"开始轮询排序，共 {len(queue_items)} 个队列项")

    # 步骤1: 提取每个队列项的时间段信息
    for item in queue_items:
        if hasattr(item, 'time_slot') and item.time_slot:
            try:
                # 从 time_slot 字段提取起始时间，格式如 "09:00-18:30"
                time_slot_beg = item.time_slot.split('-')[0].strip()
                # 转换为时间对象用于排序比较
                time_obj = datetime.strptime(time_slot_beg, "%H:%M").time()
                item.extracted_time_slot_beg = time_obj
                item.time_slot_beg_str = time_slot_beg
            except Exception as e:
                # 如果解析失败，使用默认时间
                print(f"解析队列项 {item.member} 的 time_slot 失败: {e}，使用默认时间 09:00")
                item.extracted_time_slot_beg = time(9, 0)
                item.time_slot_beg_str = "09:00"
        else:
            # 如果没有 time_slot 字段，使用默认时间
            item.extracted_time_slot_beg = time(9, 0)
            item.time_slot_beg_str = "09:00"

    # 步骤2: 按照时间段分组
    time_groups = {}
    for item in queue_items:
        time_key = item.time_slot_beg_str
        if time_key not in time_groups:
            time_groups[time_key] = []
        time_groups[time_key].append(item)

    print(f"时间分组结果: {[(time_key, len(items)) for time_key, items in time_groups.items()]}")

    # 步骤3: 按时间顺序获取所有时间段
    sorted_time_keys = sorted(time_groups.keys(), key=lambda x: datetime.strptime(x, "%H:%M").time())
    print(f"时间段排序: {sorted_time_keys}")

    # 步骤4: 为每个时间段内的队列项应用轮询算法
    time_slot_queues = {}
    for time_key in sorted_time_keys:
        time_items = time_groups[time_key]

        # 在当前时间分组内，按成员分类
        member_items = {}
        for item in time_items:
            member = item.member
            if member not in member_items:
                member_items[member] = []
            member_items[member].append(item)

        # 每个成员内按ID排序，确保成员内顺序稳定
        for member in member_items:
            member_items[member].sort(key=lambda x: x.id)

        # 应用轮询算法：A→B→C→A→B→C
        time_slot_queue = []
        sorted_members = sorted(member_items.keys())  # 按成员名排序确保一致性
        max_rounds = max(len(items) for items in member_items.values()) if member_items else 0

        # 轮询分配：从每个成员中依次取出一个元素
        for round_idx in range(max_rounds):
            for member in sorted_members:
                if round_idx < len(member_items[member]):
                    item = member_items[member][round_idx]
                    time_slot_queue.append(item)
                    print(f"轮询分配: 时间段={time_key}, 轮次={round_idx + 1}, 成员={member}")

        time_slot_queues[time_key] = time_slot_queue
        print(f"时间段 '{time_key}' 轮询排序完成，共 {len(time_slot_queue)} 个项目")

    # 步骤5: 时间段交叉轮换排序（保持轮询顺序的连续性）
    sorted_queue = []
    max_queue_length = max(len(queue) for queue in time_slot_queues.values()) if time_slot_queues else 0
    print(f"开始时间段交叉轮换排序，最大队列长度: {max_queue_length}")

    # 为每个时间段维护当前索引
    time_slot_indices = {time_key: 0 for time_key in sorted_time_keys}

    # 交叉轮换分配（跨时间段保持轮询连续性）
    for round_idx in range(max_queue_length):
        for time_key in sorted_time_keys:
            current_index = time_slot_indices[time_key]
            time_slot_queue = time_slot_queues[time_key]

            # 如果当前时间段还有未分配的队列项
            if current_index < len(time_slot_queue):
                item = time_slot_queue[current_index]
                sorted_queue.append(item)
                time_slot_indices[time_key] += 1
                print(f"交叉轮换: 轮次={round_idx + 1}, 时间段={time_key}, 成员={item.member}")

    print(f"轮询排序完成，最终队列长度: {len(sorted_queue)}")
    print(f"轮询算法确保了公平分配和可预测性，避免了随机性带来的不均匀分布")

    return sorted_queue


def sort_queue_with_random_algorithm(queue_items: List, db: Session, queue_date: date, channel_type: str):
    """
    完全随机排序算法（已弃用，建议使用轮询算法）

    解决问题：
    1. 避免同一人连续分发的问题
    2. 消除时间段聚集造成的分发不均
    3. 确保分发队列的完全随机性

    处理流程：
    1. 将队列项完全随机打乱
    2. 重新分配position字段
    3. 返回随机排序后的队列

    参数：
        queue_items: 队列项列表
        db: 数据库会话（保留兼容性）
        queue_date: 队列日期（保留兼容性）
        channel_type: 渠道类型（保留兼容性）

    返回：
        随机排序后的队列项列表
    """
    import random

    if not queue_items:
        return []

    print(f"开始随机排序，共 {len(queue_items)} 个队列项")

    # 创建队列副本，避免修改原列表
    random_queue = queue_items.copy()

    # 完全随机打乱
    random.shuffle(random_queue)

    # 打印随机排序结果的前10项作为示例
    sample_size = min(10, len(random_queue))
    print(f"随机排序结果示例（前{sample_size}项）:")
    for i, item in enumerate(random_queue[:sample_size]):
        member_name = getattr(item, 'member', 'Unknown')
        time_slot = getattr(item, 'time_slot', 'Unknown')
        group_name = getattr(item, 'group_name', 'Unknown')
        print(f"  位置{i+1}: {member_name}({group_name}) - {time_slot}")

    print(f"随机排序完成，队列长度: {len(random_queue)}")

    return random_queue


def time_slot_based_polling_algorithm(queue_items: List, db: Session, queue_date: date, channel_type: str):
    """
    基于时间段的轮询分发算法（新算法）

    核心特性：
    1. 时间优先级：按排班开始时间（time_slot_beg）升序排序所有可用人员
    2. 轮询分配：执行甲→丁→乙→丙为一轮完整循环，重复轮询直到所有数据分配完毕
    3. 跨渠道隔离：维持跨渠道隔离（新媒体付费/免费、电商渠道独立处理）
    4. 交叉排列约束：确保相邻队列不能有相同时间段或成员名
    5. 分布式队列保护：保留现有的队列更新逻辑（分布式队列不可变原则）

    算法逻辑：
    1. 提取所有队列项的时间段信息（time_slot_beg）
    2. 按时间段开始时间升序排序人员
    3. 在相同时间段内按成员名排序确保一致性
    4. 执行轮询分配：A→B→C→D→A→B→C→D...
    5. 应用交叉排列约束，确保相邻队列项不重复

    数据结构示例：
    - 人员：甲（09:00-18:30）、乙（13:30-23:59）、丙（18:30-23:59）、丁（09:30-23:59）
    - 排序后：甲（09:00）→ 丁（09:30）→ 乙（13:30）→ 丙（18:30）
    - 轮询分配：甲→丁→乙→丙→甲→丁→乙→丙...

    参数：
        queue_items: 队列项列表
        db: 数据库会话（保留兼容性）
        queue_date: 队列日期（保留兼容性）
        channel_type: 渠道类型（保留兼容性）

    返回：
        基于时间段轮询排序后的队列项列表
    """
    if not queue_items:
        return []

    print(f"开始基于时间段的轮询分发算法，共 {len(queue_items)} 个队列项")

    # 步骤1: 提取每个队列项的时间段信息
    for item in queue_items:
        if hasattr(item, 'time_slot') and item.time_slot:
            try:
                # 从 time_slot 字段提取起始时间，格式如 "09:00-18:30"
                time_slot_beg = item.time_slot.split('-')[0].strip()
                # 转换为时间对象用于排序比较
                time_obj = datetime.strptime(time_slot_beg, "%H:%M").time()
                item.extracted_time_slot_beg = time_obj
                item.time_slot_beg_str = time_slot_beg
                print(f"队列项 {item.member}: time_slot='{item.time_slot}' -> time_slot_beg='{time_slot_beg}'")
            except Exception as e:
                # 如果解析失败，使用默认时间
                print(f"解析队列项 {item.member} 的 time_slot 失败: {e}，使用默认时间 09:00")
                item.extracted_time_slot_beg = time(9, 0)
                item.time_slot_beg_str = "09:00"
        else:
            # 如果没有 time_slot 字段，使用默认时间
            print(f"队列项 {item.member} 缺少 time_slot 字段，使用默认时间 09:00")
            item.extracted_time_slot_beg = time(9, 0)
            item.time_slot_beg_str = "09:00"

    # 步骤2: 按时间段开始时间排序，然后按成员名排序确保一致性
    queue_items.sort(key=lambda x: (x.extracted_time_slot_beg, x.member))
    print(f"按时间段和成员排序完成，排序结果: {[(item.time_slot_beg_str, item.member) for item in queue_items[:10]]}")

    # 步骤3: 按时间段分组，获取每个时间段的唯一成员列表
    time_slot_members = {}
    for item in queue_items:
        time_key = item.time_slot_beg_str
        if time_key not in time_slot_members:
            time_slot_members[time_key] = set()
        time_slot_members[time_key].add(item.member)

    # 步骤4: 创建全局轮询顺序（按时间段开始时间排序的所有唯一成员）
    all_members_with_time = []
    for time_key in sorted(time_slot_members.keys(), key=lambda x: datetime.strptime(x, "%H:%M").time()):
        for member in sorted(time_slot_members[time_key]):  # 成员名排序确保一致性
            all_members_with_time.append((time_key, member))

    print(f"全局轮询顺序: {all_members_with_time}")

    # 步骤5: 按成员分组所有队列项
    member_items = {}
    for item in queue_items:
        member_key = (item.time_slot_beg_str, item.member)
        if member_key not in member_items:
            member_items[member_key] = []
        member_items[member_key].append(item)

    # 每个成员内按ID排序，确保成员内顺序稳定
    for member_key in member_items:
        member_items[member_key].sort(key=lambda x: x.id)

    # 步骤6: 执行轮询分配算法
    sorted_queue = []
    max_items_per_member = max(len(items) for items in member_items.values()) if member_items else 0

    print(f"开始轮询分配，最大成员队列长度: {max_items_per_member}")

    # 轮询分配：从每个成员中依次取出一个元素
    for round_idx in range(max_items_per_member):
        for time_key, member in all_members_with_time:
            member_key = (time_key, member)
            if member_key in member_items and round_idx < len(member_items[member_key]):
                item = member_items[member_key][round_idx]
                sorted_queue.append(item)
                print(f"轮询分配: 轮次={round_idx + 1}, 时间段={time_key}, 成员={member}")

    print(f"基于时间段的轮询分发算法完成，最终队列长度: {len(sorted_queue)}")
    print(f"算法确保了按时间段优先级的公平轮询分配，避免了时间段聚集和成员连续分发问题")

    return sorted_queue


def sort_queue_with_distributed_protection(queue_items: List, db: Session, queue_date: date, channel_type: str):
    """
    保护已分发队列的智能排序算法

    核心原则：
    1. 已分发队列不可变原则：status="distributed"的队列保持原有position和所有属性
    2. 增量保护原则：只对待分发队列进行重新排序
    3. 序号连续性原则：确保最终的position序号连续(1,2,3...)
    4. 交叉排列保持原则：新排序遵循时间段和人员交叉分布

    处理流程：
    1. 分离已分发队列和待分发队列
    2. 已分发队列保持原有position不变
    3. 待分发队列进行随机排序
    4. 重新分配连续的position序号
    5. 返回完整的排序后队列

    参数：
        queue_items: 队列项列表
        db: 数据库会话
        queue_date: 队列日期
        channel_type: 渠道类型

    返回：
        排序后的队列项列表（已分发队列保持原位，待分发队列重新排序）
    """
    import random

    if not queue_items:
        return []

    print(f"开始保护性排序，共 {len(queue_items)} 个队列项")

    # 1. 分离已分发队列和待分发队列
    distributed_items = []  # 已分发队列（不可变）
    pending_items = []      # 待分发队列（可重新排序）

    for item in queue_items:
        if item.status == "distributed":
            distributed_items.append(item)
        else:
            pending_items.append(item)

    print(f"已分发队列: {len(distributed_items)}项（保持不变）")
    print(f"待分发队列: {len(pending_items)}项（将重新排序）")

    # 2. 已分发队列按原有position排序（保持原有顺序）
    distributed_items.sort(key=lambda x: x.position)

    # 3. 待分发队列进行配置的算法排序
    if pending_items:
        # 使用当前配置的算法对待分发队列进行排序
        current_algorithm_func = get_current_queue_algorithm()
        sorted_pending_items = current_algorithm_func(pending_items, db, queue_date, channel_type)
        pending_items = sorted_pending_items
        algorithm_name = QUEUE_ALGORITHM_CONFIG["available_algorithms"][QUEUE_ALGORITHM_CONFIG["current_algorithm"]]["name"]
        print(f"待分发队列{algorithm_name}排序完成")

    # 4. 合并队列并重新分配连续的position
    final_queue = []

    # 4.1 先添加已分发队列（保持相对顺序）
    for item in distributed_items:
        final_queue.append(item)

    # 4.2 再添加待分发队列（随机排序后）
    for item in pending_items:
        final_queue.append(item)

    # 4.3 重新分配连续的position（1,2,3...）
    for new_position, item in enumerate(final_queue, 1):
        old_position = item.position
        item.position = new_position
        item.updated_at = datetime.now()

        # 只对position发生变化的项目记录日志
        if old_position != new_position:
            status_info = f"({item.status})" if item.status == "distributed" else ""
            print(f"队列项ID {item.id} {status_info} 位置调整: {old_position} -> {new_position}")

    print(f"保护性排序完成，最终队列长度: {len(final_queue)}")
    print(f"已分发队列保持原有相对顺序，待分发队列重新随机排序")

    return final_queue


def add_queue_items_incrementally(new_queue_items: List, req_data, db: Session):
    """
    增量添加队列项（对新增项应用随机排序）

    专门用于增量更新场景，确保：
    1. 现有队列完全不变（包括已分发和待分发队列）
    2. 新队列项先进行随机排序，然后追加到末尾，使用连续的position
    3. 零重排序：不对任何现有队列进行重新排序
    4. 保持原有队列的完整性和顺序
    5. 新增队列项随机排序：与生成队列时的算法保持一致，避免人员聚集

    参数：
        new_queue_items: 新增的队列项列表
        req_data: 队列更新请求数据
        db: 数据库会话

    返回：
        成功添加的队列项数量
    """
    if not new_queue_items:
        return 0

    nowtime = datetime.now()

    # 获取现有队列项的全局最大position（所有队列类型统一编号）
    max_position = db.query(func.max(DistributionQueue.position)).filter(
        DistributionQueue.queue_date == req_data.queue_date,
        DistributionQueue.channel_type == req_data.channel_type
    ).scalar() or 0

    print("当前最大position: {}, 新增队列将从position {}开始".format(max_position, max_position + 1))

    # 1. 对新增队列项应用配置的排序算法（与生成队列时保持一致）
    algorithm_name = QUEUE_ALGORITHM_CONFIG["available_algorithms"][QUEUE_ALGORITHM_CONFIG["current_algorithm"]]["name"]
    print("对新增队列项应用{}，共 {} 项".format(algorithm_name, len(new_queue_items)))

    # 创建临时队列项对象用于排序
    temp_queue_items = []
    for item in new_queue_items:
        # 创建临时对象，模拟DistributionQueue对象的属性
        class TempQueueItem:
            def __init__(self, item_data):
                self.member = item_data.get("member", "")
                self.time_slot = item_data.get("time_slot", "09:00-23:59")
                self.group_name = item_data.get("group_name", "")
                self.queue_type = item_data.get("queue_type", "free")
                self.id = hash(f"{self.member}_{self.time_slot}_{self.queue_type}")  # 临时ID
                self.original_data = item_data

        temp_queue_items.append(TempQueueItem(item))

    # 应用当前配置的排序算法
    current_algorithm_func = get_current_queue_algorithm()
    sorted_temp_items = current_algorithm_func(temp_queue_items, db, req_data.queue_date, req_data.channel_type)

    # 将排序结果转换回原始数据格式
    shuffled_new_items = [temp_item.original_data for temp_item in sorted_temp_items]

    print("新增队列项{}完成，避免人员聚集和时间段聚集，确保公平分配".format(algorithm_name))

    # 2. 处理随机排序后的新增队列项（统一分配position）
    added_count = 0
    current_position = max_position + 1

    # 统计新增队列类型
    free_count = len([item for item in shuffled_new_items if item["queue_type"] == "free"])
    paid_count = len([item for item in shuffled_new_items if item["queue_type"] == "paid"])

    # 按照随机排序后的顺序分配position
    for item in shuffled_new_items:
        position = current_position
        queue_type = item.get("queue_type", "free")

        # 解析时间段
        time_slot = item.get("time_slot", "09:00-23:59")
        try:
            time_parts = time_slot.split("-")
            start_time_str = time_parts[0].strip()
            end_time_str = time_parts[1].strip() if len(time_parts) > 1 else "23:59"

            start_time = datetime.strptime(start_time_str, "%H:%M").time()
            end_time = datetime.strptime(end_time_str, "%H:%M").time()

            combined_beg = datetime.combine(req_data.queue_date, start_time)
            combined_end = datetime.combine(req_data.queue_date, end_time)
        except:
            combined_beg = datetime.combine(req_data.queue_date, datetime.min.time().replace(hour=9))
            combined_end = datetime.combine(req_data.queue_date, datetime.min.time().replace(hour=23, minute=59))

        queue_item = DistributionQueue(
            queue_date=req_data.queue_date,
            channel_type=req_data.channel_type,
            queue_type=queue_type,
            position=position,
            group_name=item.get("group_name", ""),
            leader=item.get("leader", ""),
            member=item.get("member", ""),
            time_slot=time_slot,
            status="pending",
            created_at=nowtime,
            updated_at=nowtime,
            time_slot_beg=combined_beg,
            time_slot_end=combined_end
        )
        db.add(queue_item)
        added_count += 1
        current_position += 1  # 递增position
        print("增量添加{}队列项: position={}, member={}".format(queue_type, position, item.get('member', '')))

    print("增量添加完成: 免费队列{}项, 付费队列{}项, 总计{}项".format(free_count, paid_count, added_count))
    print("新增队列项position范围: {}-{}".format(max_position + 1, max_position + added_count))
    print("新增队列项已应用随机排序，与生成队列时的算法保持一致")

    return added_count

@router.get("/api/distribution/queue/data")
def get_saved_distribution_queue(
    queue_date: date = Query(..., description="队列日期"),
    channel_type: str = Query(..., description="渠道类型"),
    db: Session = Depends(get_db)
):
    """获取已保存的分发队列数据

    直接按position顺序返回队列数据：
    1. 查询数据库中已保存的队列数据
    2. 按position字段升序排序返回
    3. 不进行重新排序，保持队列顺序的稳定性
    4. 队列的排序只在生成、更新、保存、重排序时进行
    5. 获取数据时不改变队列顺序，确保刷新页面后顺序不变
    """
    try:
        # 查询该日期和渠道类型的队列数据，按position升序排序
        free_queue_items = db.query(DistributionQueue).filter(
            DistributionQueue.queue_date == queue_date,
            DistributionQueue.channel_type == channel_type,
            DistributionQueue.queue_type == "free"
        ).order_by(DistributionQueue.position.asc()).all()
        
        paid_queue_items = db.query(DistributionQueue).filter(
            DistributionQueue.queue_date == queue_date,
            DistributionQueue.channel_type == channel_type,
            DistributionQueue.queue_type == "paid"
        ).order_by(DistributionQueue.position.asc()).all()
        
        print(f"获取队列数据: {channel_type} {queue_date} - 免费队列 {len(free_queue_items)} 项，付费队列 {len(paid_queue_items)} 项")
        
        result = {
            "free_queue": [item.to_dict() for item in free_queue_items],
            "paid_queue": [item.to_dict() for item in paid_queue_items],
            "queue_date": queue_date,
            "channel_type": channel_type,
            "total_free": len(free_queue_items),
            "total_paid": len(paid_queue_items)
        }
        
        return result
    except Exception as e:
        print(f"获取分发队列失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取分发队列失败: {str(e)}")

@router.post("/api/distribution/queue/reorder")
def reorder_distribution_queue(
    queue_date: date = Query(..., description="队列日期"),
    channel_type: str = Query(..., description="渠道类型"),
    db: Session = Depends(get_db)
):
    """重新排序分发队列数据（保护已分发队列版本）

    基于保护已分发队列的智能排序算法：
    1. 已分发队列不可变：保持原有position和所有属性
    2. 待分发队列随机排序：避免同一人连续分发的问题
    3. 序号连续性：确保最终position序号连续(1,2,3...)
    4. 保护核心原则：已分发的队列绝对不能被修改
    5. 支持按时间段分发：分发系统会从队列第一个开始检查时间段
    6. 时间段匹配：符合当前时间段就分发，不符合就跳到下一个
    7. 智能排序确保不会破坏已分发队列的完整性
    """
    try:
        reordered_counts = {"free": 0, "paid": 0}

        # 分别处理免费队列和付费队列
        for queue_type in ["free", "paid"]:
            # 查询该队列类型的所有项
            queue_items = db.query(DistributionQueue).filter(
                DistributionQueue.queue_date == queue_date,
                DistributionQueue.channel_type == channel_type,
                DistributionQueue.queue_type == queue_type
            ).all()

            if queue_items:
                # 使用保护已分发队列的智能排序算法
                sorted_items = sort_queue_with_distributed_protection(queue_items, db, queue_date, channel_type)
                # 注意：新算法内部已经处理了position分配

                reordered_counts[queue_type] = len(sorted_items)
                print(f"智能排序 {queue_type} 队列完成，共 {len(sorted_items)} 项（已分发队列保持不变）")
        
        # 提交更新
        db.commit()
        
        return {
            "status": "success",
            "message": f"队列智能排序成功（已分发队列保持不变），免费队列: {reordered_counts['free']} 项，付费队列: {reordered_counts['paid']} 项",
            "reordered_counts": reordered_counts
        }
        
    except Exception as e:
        db.rollback()
        print(f"重新排序队列失败: {e}")
        raise HTTPException(status_code=500, detail=f"重新排序队列失败: {str(e)}")

@router.put("/api/distribution/queue/update-status/{queue_id}", response_model=None)
def update_queue_item_status(
    request: Request,
    queue_id: int,
    background_tasks: BackgroundTasks,
    status: str = Query(..., description="新状态，可选值: pending, distributed, cancelled"),
    remark: Optional[str] = Query(None, description="备注信息"),
    db: Session = Depends(get_db)
):
    """更新队列项的状态"""
    try:
        # 获取当前用户
        current_user = get_current_user(request, db)
        if not current_user:
            raise HTTPException(status_code=401, detail="用户未登录")
            
        # 查找队列项
        queue_item = db.query(DistributionQueue).filter(DistributionQueue.id == queue_id).first()
        if not queue_item:
            raise HTTPException(status_code=404, detail="队列项不存在")
        
        # 更新状态
        queue_item.status = status
        if status == "distributed":
            queue_item.distributed_at = datetime.now()
            queue_item.distributed_by = current_user.name
        
        # 更新备注
        if remark is not None:
            queue_item.remark = remark
        
        queue_item.updated_at = datetime.now()
        
        db.commit()
        
        # 在后台任务中处理待分发线索
        background_tasks.add_task(process_pending_leads_background, db)
        
        return {"status": "success", "message": "队列项状态更新成功，已开始处理待分发线索"}
    except Exception as e:
        db.rollback()
        print(f"更新队列项状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新队列项状态失败: {str(e)}")

@router.get("/api/distribution/queue/statistics")
def get_distribution_queue_statistics(
    queue_date: date = Query(..., description="队列日期"),
    channel_type: str = Query(..., description="渠道类型"),
    db: Session = Depends(get_db)
):
    """获取分发队列统计信息"""
    try:
        # 查询免费队列统计
        free_total = db.query(func.count(DistributionQueue.id)).filter(
            DistributionQueue.queue_date == queue_date,
            DistributionQueue.channel_type == channel_type,
            DistributionQueue.queue_type == "free"
        ).scalar() or 0
        
        free_distributed = db.query(func.count(DistributionQueue.id)).filter(
            DistributionQueue.queue_date == queue_date,
            DistributionQueue.channel_type == channel_type,
            DistributionQueue.queue_type == "free",
            DistributionQueue.status == "distributed"
        ).scalar() or 0
        
        # 查询付费队列统计
        paid_total = db.query(func.count(DistributionQueue.id)).filter(
            DistributionQueue.queue_date == queue_date,
            DistributionQueue.channel_type == channel_type,
            DistributionQueue.queue_type == "paid"
        ).scalar() or 0
        
        paid_distributed = db.query(func.count(DistributionQueue.id)).filter(
            DistributionQueue.queue_date == queue_date,
            DistributionQueue.channel_type == channel_type,
            DistributionQueue.queue_type == "paid",
            DistributionQueue.status == "distributed"
        ).scalar() or 0
        
        # 按成员分组统计已分发数量
        member_stats = db.query(
            DistributionQueue.member,
            func.count(DistributionQueue.id).label("total_distributed")
        ).filter(
            DistributionQueue.queue_date == queue_date,
            DistributionQueue.channel_type == channel_type,
            DistributionQueue.status == "distributed"
        ).group_by(DistributionQueue.member).all()
        
        member_statistics = [
            {"member": stat.member, "total_distributed": stat.total_distributed}
            for stat in member_stats
        ]
        
        return {
            "free_total": free_total,
            "free_distributed": free_distributed,
            "free_remaining": free_total - free_distributed,
            "paid_total": paid_total,
            "paid_distributed": paid_distributed,
            "paid_remaining": paid_total - paid_distributed,
            "member_statistics": member_statistics
        }
    except Exception as e:
        print(f"获取队列统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取队列统计信息失败: {str(e)}")

@router.delete("/api/distribution/queue/delete")
def delete_distribution_queue(
    background_tasks: BackgroundTasks,
    queue_date: date = Query(..., description="队列日期"),
    channel_type: str = Query(..., description="渠道类型"),
    db: Session = Depends(get_db)
):
    """删除分发队列数据"""
    try:
        # 打印调试信息
        print(f"正在删除队列数据: 日期={queue_date}, 渠道类型={channel_type}")
        
        # 查询该日期和渠道类型的队列数据，打印数量
        free_queue_count = db.query(func.count(DistributionQueue.id)).filter(
            DistributionQueue.queue_date == queue_date,
            DistributionQueue.channel_type == channel_type,
            DistributionQueue.queue_type == "free"
        ).scalar() or 0
        
        paid_queue_count = db.query(func.count(DistributionQueue.id)).filter(
            DistributionQueue.queue_date == queue_date,
            DistributionQueue.channel_type == channel_type,
            DistributionQueue.queue_type == "paid"
        ).scalar() or 0
        
        print(f"删除前队列数据统计: 免费队列={free_queue_count}条, 付费队列={paid_queue_count}条")
        
        # 删除该日期和渠道类型的队列数据
        deleted_rows = db.query(DistributionQueue).filter(
            DistributionQueue.queue_date == queue_date,
            DistributionQueue.channel_type == channel_type
        ).delete(synchronize_session=False)
        
        # 新增：清零销售排班表中的实际接待数
        updated_rules = db.query(DistributionRule).filter(
            DistributionRule.date == queue_date,
            DistributionRule.channel_type == channel_type
        ).update({
            "actual_total": 0,
            "actual_free": 0,
            "actual_paid": 0
        }, synchronize_session=False)
        
        print(f"清零了 {updated_rules} 条销售排班记录的实际接待数")
        
        # 新增：清零渠道接待表中的实际接待数
        updated_channels = db.query(ChannelReception).filter(
            ChannelReception.rule_date == queue_date,
            ChannelReception.channel_type == channel_type
        ).update({
            "actual_reception": 0,
            "actual_free_reception": 0,
            "actual_paid_reception": 0
        }, synchronize_session=False)
        
        print(f"清零了 {updated_channels} 条渠道接待记录的实际接待数")
        
        # 提交事务
        db.commit()
        
        # 再次查询确认已删除
        post_free_count = db.query(func.count(DistributionQueue.id)).filter(
            DistributionQueue.queue_date == queue_date,
            DistributionQueue.channel_type == channel_type,
            DistributionQueue.queue_type == "free"
        ).scalar() or 0
        
        post_paid_count = db.query(func.count(DistributionQueue.id)).filter(
            DistributionQueue.queue_date == queue_date,
            DistributionQueue.channel_type == channel_type,
            DistributionQueue.queue_type == "paid"
        ).scalar() or 0
        
        print(f"删除后队列数据统计: 免费队列={post_free_count}条, 付费队列={post_paid_count}条")
        print(f"成功删除了 {deleted_rows} 条队列数据")
        
        # 在后台任务中处理待分发线索
        background_tasks.add_task(process_pending_leads_background, db)
        
        return {
            "status": "success", 
            "message": f"队列删除成功，共删除{deleted_rows}条数据，已清零{updated_rules}条排班记录和{updated_channels}条渠道记录的实际接待数，已开始处理待分发线索",
            "deleted_count": deleted_rows,
            "reset_rules_count": updated_rules,
            "reset_channels_count": updated_channels,
            "pre_delete": {"free": free_queue_count, "paid": paid_queue_count},
            "post_delete": {"free": post_free_count, "paid": post_paid_count}
        }
    except Exception as e:
        db.rollback()
        print(f"删除分发队列失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除分发队列失败: {str(e)}")

# 新媒体渠道自动分发API接口
class MediaDistributionRequest(BaseModel):
    """线索分发请求数据模型"""
    items: List[Dict[str, Any]]  # 接收的一组或多组数据

@router.post("/api/distribution/media/auto-dispatch")
async def auto_dispatch_leads(
    data: MediaDistributionRequest,
    db: Session = Depends(get_db)
):

    """
    线索自动分发API
    根据传入的一个或多个线索ID，查询相应数据并根据渠道类型和队列进行分发
    支持电商渠道和新媒体渠道
    
    新媒体渠道支持店铺验证：
    - 如果店铺验证通过，根据返回的成员名字使用"插队"方式分发
    - 如果店铺验证失败，使用原有的队列顺序分发
    
    新媒体渠道支持队列转移：
    - 当原队列类型(免费/付费)已满，可以转移到另一队列类型继续分发
    - 转移时按顺序分发，不插队
    - 仅限新媒体渠道内的队列互转，电商渠道不支持转移
    
    状态更新规则：
    - 无论分发成功或失败，都将status字段设为2（等待中）
    - 分发成功后，额外将type字段设为2，并将status字段设为3（已发送）
    """
    try:
        # 获取当前日期
        current_date_now = datetime.now()
        current_date = datetime.now().date()

        # 验证请求数据
        if not data.items or len(data.items) == 0:
            raise HTTPException(status_code=400, detail="未提供有效的数据项")
        
        # 日志记录接收到的数据
        print(f"接收到自动分发请求，包含 {len(data.items)} 条数据项")

        # 提取所有线索ID
        lead_ids = []
        for item in data.items:
            # 尝试从不同可能的字段获取ID
            lead_id = item.get("ID") or item.get("id") or item.get("lead_id")
            if lead_id:
                lead_ids.append(lead_id)

        if not lead_ids:
            return {
                "success": False,
                "message": "未找到有效的线索ID",
                "results": [],
                "errors": ["请求数据中未包含有效的线索ID"],
                "total_processed": 0,
                "successful": 0,
                "failed": len(data.items)
            }

        #查询两天内所有未分发的数据
        two_day_nei_date = current_date - timedelta(days=1)
        # 查询这些ID对应的线索数据
        leads = db.query(ClueSheet).filter(
            ClueSheet.record_time >= two_day_nei_date,
            ClueSheet.status.in_([1,2]),
            ClueSheet.is_deleted == 0
        ).order_by(ClueSheet.record_time.desc()).all()

        if not leads:
            return {
                "success": False,
                "message": f"未找到待分发的线索记录: {lead_ids}",
                "results": [],
                "errors": ["未找到待分发的线索记录"],
                "total_processed": 0,
                "successful": 0,
                "failed": len(lead_ids)
            }
        
        # 处理结果记录
        results =  errors = []
        successful_count = failed_count = 0

        # 处理每个线索
        for lead in leads:

            try:
                # 无论成功或失败，先将状态设置为等待中(2)
                lead.status = 2
                db.commit()
                # 根据线索渠道确定渠道类型
                if lead.channel == "电商":
                    channel_type = "电商渠道"
                elif lead.channel == "新媒体":
                    channel_type = "新媒体渠道"
                elif lead.channel == "公海":
                    channel_type = "公海渠道"
                else:
                    error_msg = f"不支持的渠道类型：{lead.channel}，线索ID: {lead.ID}"
                    errors.append(error_msg)
                    results.append({
                        "item": {"id": lead.ID},
                        "status": "failed",
                        "message": f"不支持的渠道类型：{lead.channel}"
                    })
                    failed_count += 1
                    continue
                
                # 根据队列字段确定队列类型（0:付费队列，1:免费队列）
                initial_queue_type = "paid" if lead.queue == "0" else "free"
                
                # 新增: 如果是新媒体渠道，执行店铺验证逻辑
                matched_member = None
                matched_member_arr = []
                if lead.channel == "新媒体":

                    print(f"执行店铺验证，线索ID: {lead.ID}, 店铺: {lead.store}")

                    # 优化店铺验证逻辑: 查询当日销售排班中新媒体渠道的排班数据
                    store_rules = db.query(DistributionRule).filter(
                        DistributionRule.date == current_date,
                        current_date_now >= DistributionRule.time_range_start,
                        current_date_now <= DistributionRule.time_range_end,
                        DistributionRule.channel_type == "新媒体渠道"
                    ).order_by(DistributionRule.created_at.desc()).all()
                   
                    # 遍历排班数据，检查店铺是否匹配
                    matched_rule = None
                    if store_rules:
                        for rule in store_rules:
                            # 分割排班规则中的店铺字段(可能包含多个店铺，用逗号分隔)
                            rule_stores = [store.strip() for store in rule.store.split(',')]
                            
                            # 检查线索店铺是否在规则的店铺列表中
                            if lead.store in rule_stores:
                                #return rule.member
                                # 将新标签追加到 tags 数组中
                                if rule.member:
                                    matched_member_arr.append(rule.member)
                                
                                matched_rule = rule
                                print(f"店铺验证成功: '{lead.store}' 匹配规则中的店铺列表 {rule_stores}, 成员: {rule.member}")
                                #break
                    
                    # 如果找到匹配的排班规则，记录对应的成员
                    if matched_rule:
                        matched_member = matched_rule.member
                        print(f"店铺验证成功，找到匹配成员: {matched_member}")
                    else:
                        print(f"店铺验证失败，没有找到匹配的店铺: {lead.store}")
                
                # 处理 not_admin 字段：将逗号分隔的字符串转换为列表
                excluded_members = []
                if lead.not_admin:
                    # 拆分字符串并清理空格
                    excluded_members = [member.strip() for member in lead.not_admin.split(',') if member.strip()]
                    print(f"线索ID: {lead.ID} 排除成员列表: {excluded_members}")
                
                # 分发逻辑: 根据店铺验证结果选择不同的分发方式
                available_queue = None
                used_queue_type = initial_queue_type
                is_queue_transferred = False
                distribution_method = "常规队列分发"
               
                if lead.channel == "新媒体" and matched_member_arr:
                    # 店铺验证成功: 查找匹配成员名字、队列号最小且状态为待分发的数据行
                    # 同时排除 not_admin 中的成员
                    query = db.query(DistributionQueue).filter(
                        DistributionQueue.queue_date == current_date,
                        DistributionQueue.channel_type == channel_type,
                        DistributionQueue.queue_type == initial_queue_type,
                        DistributionQueue.member.in_(matched_member_arr),
                        DistributionQueue.status == "pending",
                        current_date_now >= DistributionQueue.time_slot_beg,
                        current_date_now <= DistributionQueue.time_slot_end
                    )
                    if excluded_members:
                        query = query.filter(~DistributionQueue.member.in_(excluded_members))
                    
                    available_queue = query.order_by(DistributionQueue.position).first()
                    
                    if available_queue:
                        distribution_method = "店铺匹配分发"
                    else:
                        # 如果找不到匹配的成员队列项，则降级到常规分发方式
                        print(f"未找到匹配成员 {matched_member} 的待分发队列项，切换到常规分发方式")
                
                # 如果没有匹配的成员队列项或不是新媒体渠道/店铺验证失败，使用原有的分发方式
                if not available_queue:
                   
                    # 常规分发查询：排除 not_admin 中的成员
                    query = db.query(DistributionQueue).filter(
                        DistributionQueue.queue_date == current_date,
                        DistributionQueue.channel_type == channel_type,
                        DistributionQueue.queue_type == initial_queue_type,
                        DistributionQueue.status == "pending",
                        current_date_now >= DistributionQueue.time_slot_beg,
                        current_date_now <= DistributionQueue.time_slot_end,
                    )

                    if excluded_members:
                        query = query.filter(~DistributionQueue.member.in_(excluded_members))
                   
                    available_queue = query.order_by(DistributionQueue.position).first()
                
                # 新增: 队列转移逻辑 - 适用于新媒体渠道
                """
                if not available_queue and channel_type == "新媒体渠道":
                    
                    # 初始队列类型已满，尝试转移到另一个队列类型
                    alternate_queue_type = "free" if initial_queue_type == "paid" else "paid"
                    print(f"当前队列类型 {initial_queue_type} 已满，尝试转移到 {alternate_queue_type} 队列")
                    
                    # 在另一种队列类型中查找可用队列项(不进行成员匹配，按顺序分发)
                    # 同样排除 not_admin 中的成员
                    query = db.query(DistributionQueue).filter(
                        DistributionQueue.queue_date == current_date,
                        DistributionQueue.channel_type == channel_type,
                        DistributionQueue.queue_type == alternate_queue_type,
                        DistributionQueue.status == "pending",
                        current_date_now >= DistributionQueue.time_slot_beg,
                        current_date_now <= DistributionQueue.time_slot_end,
                    )
                    if excluded_members:
                        query = query.filter(~DistributionQueue.member.in_(excluded_members))
                    
                    available_queue = query.order_by(DistributionQueue.position).first()
                   
                    if available_queue:
                        used_queue_type = alternate_queue_type
                        is_queue_transferred = True
                        distribution_method = "队列转移分发"
                        print(f"成功转移到 {alternate_queue_type} 队列")
                    else:
                        print(f"转移失败，{alternate_queue_type} 队列也已满")
                """
                if not available_queue:
                    #error_msg = f"未找到可用的分发队列，线索ID: {lead.ID}，渠道类型: {channel_type}，尝试的队列类型: {initial_queue_type}" + (f", {alternate_queue_type}" if channel_type == "新媒体渠道" else "")
                    error_msg = f"未找到可用的分发队列，线索ID: {lead.ID}，渠道类型: {channel_type}"

                    errors.append(error_msg)
                    results.append({
                        "item": {"id": lead.ID},
                        "status": "failed",
                        "message": "未找到可用的分发队列"
                    })
                    failed_count += 1
                    # 不需要重置status字段，因为已经在前面设置为2（等待中）
                    db.commit()
                    continue
                #return available_queue
                # 更新分发队列状态
                available_queue.status = "distributed"
                available_queue.distributed_at = datetime.now()
                available_queue.distributed_by = "系统自动分发"
                available_queue.remark = f"自动分发线索ID: {lead.ID}" + (f" (队列转移: {initial_queue_type} → {used_queue_type})" if is_queue_transferred else "")
                
                # 分发成功后，更新线索对接人字段为队列成员
                lead.contact_person = available_queue.member

                # 修改SalaCrm数据，分配时间和序号SN
                # 查询 在当天+此销售+范围内的分配数
                withdraw_condition = or_(
                    ClueSheet.contact_person == lead.contact_person,  # 单人情况：直接匹配
                    func.CONCAT(',', ClueSheet.withdraw_contact_person, ',').like(f"%,{lead.contact_person},%")  # 多人情况：逗号分隔字符串中包含
                )
                list_num = db.query(ClueSheet).filter(func.DATE(ClueSheet.record_time) == current_date,withdraw_condition).order_by(ClueSheet.record_time.desc()).count()
               
                if not list_num: list_num = 0
                db_sala_crm = db.query(SalaCrm).filter(SalaCrm.ID == lead.ID).order_by(SalaCrm.last_followup_time.desc()).first()
                if db_sala_crm:
                    db_sala_crm.allocation_date = datetime.now()
                    db_sala_crm.SN = list_num + 1
                    db.commit()

                # 设置type字段为2
                lead.type = "2"
                
                # 设置status字段为3（已发送）
                lead.status = 3
                # 使用辅助函数更新销售排班页面中的实际接待数
                await update_actual_reception_count(
                    db, 
                    current_date, 
                    channel_type, 
                    available_queue.member, 
                    used_queue_type,
                    is_transferred=is_queue_transferred,
                    original_queue_type=initial_queue_type if is_queue_transferred else None
                )
                # 保存更改
                db.commit()
                
                # 记录成功
                queue_info = f"{used_queue_type}队列" + (" (由 {initial_queue_type} 转移)" if is_queue_transferred else "")
                results.append({
                    "item": {"id": lead.ID},
                    "status": "success",
                    "message": f"成功分发到队列: {available_queue.group_name}-{available_queue.member} ({distribution_method}, {queue_info})"
                })
                successful_count += 1

                
            except Exception as e:
                db.rollback()
                # 异常处理时，确保status字段设为2
                try:
                    lead.status = 2
                    db.commit()
                    print(f"分发异常，将线索ID: {lead.ID} 的状态设置为等待中(2)")
                except Exception as inner_e:
                    print(f"更新线索状态失败: {inner_e}")
                
                error_msg = f"处理线索ID {lead.ID} 时出错: {str(e)}"
                errors.append(error_msg)
                results.append({
                    "item": {"id": lead.ID},
                    "status": "failed",
                    "message": str(e)
                })
                failed_count += 1

        # 如果有成功分发的线索，自动触发WebSocket推送
        if successful_count > 0:
            try:
                print(f"🚀 开始自动触发WebSocket推送，成功分发了 {successful_count} 条线索")

                # 通过HTTP客户端调用WebSocket推送接口
                async with httpx.AsyncClient() as client:
                    # 构建推送接口的完整URL
                    push_url = f"{WEB_URL}/api/websocket/xiansuo"
                    #print(f"📡 调用推送接口: {push_url}")

                    # 发送GET请求到推送接口，设置超时时间
                    response = await client.get(push_url, timeout=15.0)

                    if response.status_code == 200:
                        push_result = response.json()
                        #print(f"✅ WebSocket推送调用成功: {push_result.get('message', '推送完成')}")
                        print(f"📊 推送详情: 成功推送给 {push_result.get('pushed_users', 0)} 个在线用户，总共 {push_result.get('total_clues', 0)} 条线索")
                        #print(f"🌐 HTTP状态码: {response.status_code}")
                    else:
                        print(f"❌ WebSocket推送调用失败，HTTP状态码: {response.status_code}")
                        #print(f"📝 响应内容: {response.text}")

            except httpx.TimeoutException:
                # 超时异常单独处理
                print(f"⏰ WebSocket推送调用超时（15秒），但不影响分发流程")
            except httpx.RequestError as req_error:
                # 网络请求异常
                print(f"🌐 WebSocket推送网络请求失败: {str(req_error)}")
            except Exception as push_error:
                # WebSocket推送失败不影响主要的分发流程，确保错误隔离
                print(f"⚠️ 调用WebSocket推送时出错: {str(push_error)}")
                print(f"📝 推送错误详情: {type(push_error).__name__}")
                # 记录详细的错误信息，但不抛出异常
                import traceback
                print(f"🔍 推送错误堆栈: {traceback.format_exc()}")

        return {
            "success": successful_count > 0,
            "message": f"处理了 {len(leads)} 条线索，成功: {successful_count}，失败: {failed_count}",
            "results": results,
            "errors": errors,
            "total_processed": len(leads),
            "successful": successful_count,
            "failed": failed_count
        }
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"处理自动分发请求时出错: {str(e)}"
        print(f"异常: {error_msg}")
        import traceback
        print(f"堆栈跟踪: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=error_msg)

# 添加新的API接口处理待分发线索
@router.post("/api/distribution/process-pending-leads")
async def process_pending_leads(
    db: Session = Depends(get_db)
):
    """
    查找状态为等待中(status=2)的线索并自动分发
    - 只处理当天和前一天的数据
    - 将查找到的线索ID传递给auto-dispatch接口进行重新分发
    """
    try:
        # 获取当前日期和前一天日期
        today = datetime.now().date()
        yesterday = today - timedelta(days=1)
        
        # 转换为字符串格式，方便查询
        today_str = today.strftime("%Y%m%d")
        yesterday_str = yesterday.strftime("%Y%m%d")
        
        # 查询status=2且ID前缀为当天或前一天的线索
        pending_leads = db.query(ClueSheet).filter(
            ClueSheet.status == 2,
            ClueSheet.is_deleted == False,
            or_(
                ClueSheet.ID.like(f"{today_str}%"),
                ClueSheet.ID.like(f"{yesterday_str}%")
            )
        ).all()
        
        if not pending_leads:
            return {
                "success": True, 
                "message": "没有找到待处理的线索",
                "count": 0
            }
        
        # 提取线索ID
        lead_ids = [lead.ID for lead in pending_leads]
        
        print(f"找到 {len(lead_ids)} 条待处理线索: {lead_ids}")
        
        # 构建请求数据
        dispatch_data = MediaDistributionRequest(
            items=[{"ID": lead_id} for lead_id in lead_ids]
        )
        
        # 调用auto-dispatch接口进行分发
        dispatch_result = await auto_dispatch_leads(dispatch_data, db)
        
        return {
            "success": True,
            "message": f"成功处理待分发线索，共 {len(lead_ids)} 条",
            "processed_count": len(lead_ids),
            "dispatch_result": dispatch_result
        }
        
    except Exception as e:
        error_msg = f"处理待分发线索时出错: {str(e)}"
        print(f"异常: {error_msg}")
        import traceback
        print(f"堆栈跟踪: {traceback.format_exc()}")
        return {
            "success": False,
            "message": error_msg,
            "count": 0
        }

# 添加一个后台任务函数，用于异步处理待分发线索
async def process_pending_leads_background(db: Session):
    """后台任务处理待分发线索"""
    try:
        await process_pending_leads(db)
    except Exception as e:
        print(f"后台处理待分发线索时出错: {str(e)}")

# 新增：完整的队列生成和保存API
class QueueGenerationRequest(BaseModel):
    queue_date: date
    channel_type: str

@router.post("/api/distribution/queue/generate-and-save")
def generate_and_save_queue(
    request: QueueGenerationRequest,
    db: Session = Depends(get_db)
):
    """
    生成并保存分发队列（应用时间段交叉排序）

    这个API完全在后端处理队列生成、排序和保存，确保：
    1. 使用后端的队列生成逻辑
    2. 立即应用时间段交叉排序算法
    3. 返回已排序的队列数据给前端
    4. 前端无需进行任何队列处理
    """
    try:
        # 检查是否已存在队列
        existing_queue = db.query(DistributionQueue).filter(
            DistributionQueue.queue_date == request.queue_date,
            DistributionQueue.channel_type == request.channel_type
        ).first()

        if existing_queue:
            raise HTTPException(status_code=400, detail=f"{request.queue_date} 的 {request.channel_type} 队列已存在")

        # 直接在这里实现队列生成逻辑，确保立即应用时间段交叉排序
        from models import DistributionRule

        # 查询指定日期和渠道类型的规则
        rules = db.query(DistributionRule).filter(
            DistributionRule.date == request.queue_date,
            DistributionRule.channel_type == request.channel_type,
            DistributionRule.is_deleted == 0
        ).all()
        if not rules:
            raise HTTPException(status_code=404, detail=f"未找到 {request.queue_date} {request.channel_type} 的分发规则")

        # 删除已有的队列数据
        db.query(DistributionQueue).filter(
            DistributionQueue.queue_date == request.queue_date,
            DistributionQueue.channel_type == request.channel_type
        ).delete()

        # 按组聚合规则并生成队列
        from collections import defaultdict
        groups = defaultdict(list)
        for rule in rules:
            groups[rule.group_name].append(rule)

        # 生成免费和付费队列项（不分配position，稍后统一排序）
        free_queue_items = []
        paid_queue_items = []

        # 修复版：按每个成员的具体expected_free和expected_paid值生成队列项
        for group_name, group_rules in groups.items():
            # 按时间段排序组内规则
            sorted_rules = sorted(group_rules, key=lambda x: x.time_range)

            print(f"处理分组: {group_name}, 渠道类型: {request.channel_type}")

            # 为每个成员根据其具体的expected_free和expected_paid值生成队列项
            for rule in sorted_rules:
                member_name = rule.member
                expected_free = rule.expected_free or 0
                expected_paid = rule.expected_paid or 0

                print(f"  成员: {member_name}, 免费队列: {expected_free}条, 付费队列: {expected_paid}条")

                # 处理电商渠道和公海渠道的特殊逻辑
                if request.channel_type in ["电商渠道", "公海渠道"]:
                    # 电商渠道和公海渠道特殊处理：如果expected_free为0，则使用expected_total作为免费队列
                    if expected_free == 0 and rule.expected_total > 0:
                        expected_free = rule.expected_total
                        print(f"    {request.channel_type}特殊处理: {member_name} 免费队列调整为 {expected_free}条")

                    # 电商渠道和公海渠道通常不生成付费队列，除非明确设置了expected_paid > 0
                    if expected_paid == 0:
                        print(f"    {request.channel_type}: {member_name} 跳过付费队列生成")

                # 生成免费队列项
                for i in range(expected_free):
                    queue_item = DistributionQueue(
                        queue_date=request.queue_date,
                        channel_type=request.channel_type,
                        queue_type="free",
                        position=0,  # 临时设置为0，稍后重新分配
                        group_name=group_name,
                        leader=rule.leader,
                        member=member_name,
                        time_slot=rule.time_range,
                        status="pending",
                        created_at=datetime.now(),
                        updated_at=datetime.now(),
                        time_slot_beg=rule.time_range_start,
                        time_slot_end=rule.time_range_end
                    )
                    free_queue_items.append(queue_item)
                    print(f"    生成免费队列项: {member_name} (第{i+1}条)")

                # 生成付费队列项
                for i in range(expected_paid):
                    queue_item = DistributionQueue(
                        queue_date=request.queue_date,
                        channel_type=request.channel_type,
                        queue_type="paid",
                        position=0,  # 临时设置为0，稍后重新分配
                        group_name=group_name,
                        leader=rule.leader,
                        member=member_name,
                        time_slot=rule.time_range,
                        status="pending",
                        created_at=datetime.now(),
                        updated_at=datetime.now(),
                        time_slot_beg=rule.time_range_start,
                        time_slot_end=rule.time_range_end
                    )
                    paid_queue_items.append(queue_item)
                    print(f"    生成付费队列项: {member_name} (第{i+1}条)")

        print(f"队列生成完成: 免费队列 {len(free_queue_items)} 项, 付费队列 {len(paid_queue_items)} 项")

        # 保存到数据库（position稍后更新）
        for item in free_queue_items + paid_queue_items:
            db.add(item)
        db.commit()

        # 重新获取保存后的队列项（包含ID）
        free_queue_items = db.query(DistributionQueue).filter(
            DistributionQueue.queue_date == request.queue_date,
            DistributionQueue.channel_type == request.channel_type,
            DistributionQueue.queue_type == "free"
        ).all()

        paid_queue_items = db.query(DistributionQueue).filter(
            DistributionQueue.queue_date == request.queue_date,
            DistributionQueue.channel_type == request.channel_type,
            DistributionQueue.queue_type == "paid"
        ).all()

        # 应用配置的排序算法
        current_algorithm_func = get_current_queue_algorithm()
        algorithm_name = QUEUE_ALGORITHM_CONFIG["available_algorithms"][QUEUE_ALGORITHM_CONFIG["current_algorithm"]]["name"]

        print(f"对新生成的免费队列应用{algorithm_name}...")
        sorted_free_queue = current_algorithm_func(free_queue_items, db, request.queue_date, request.channel_type)

        print(f"对新生成的付费队列应用{algorithm_name}...")
        sorted_paid_queue = current_algorithm_func(paid_queue_items, db, request.queue_date, request.channel_type)

        # 重新分配position字段
        for position, item in enumerate(sorted_free_queue, 1):
            item.position = position
            item.updated_at = datetime.now()

        for position, item in enumerate(sorted_paid_queue, 1):
            item.position = position
            item.updated_at = datetime.now()

        # 提交排序更新
        db.commit()

        # 返回排序后的队列数据
        result = {
            "success": True,
            "message": f"成功生成并保存 {request.channel_type} 分发队列，已应用时间段交叉排序",
            "queue_date": request.queue_date,
            "channel_type": request.channel_type,
            "free_queue": [item.to_dict() for item in sorted_free_queue],
            "paid_queue": [item.to_dict() for item in sorted_paid_queue],
            "total_free": len(sorted_free_queue),
            "total_paid": len(sorted_paid_queue),
            "total_items": len(sorted_free_queue) + len(sorted_paid_queue),
            "algorithm_description": QUEUE_ALGORITHM_CONFIG["available_algorithms"][QUEUE_ALGORITHM_CONFIG["current_algorithm"]]["description"]
        }

        return result

    except Exception as e:
        db.rollback()
        print(f"生成并保存队列失败: {e}")
        raise HTTPException(status_code=500, detail=f"生成并保存队列失败: {str(e)}")

# 保留辅助函数定义以避免潜在的引用错误
def is_time_in_range(time_to_check, start_time, end_time):
    """
    检查指定时间是否在时间范围内
    处理跨日问题，例如 23:00-01:00
    """
    if start_time <= end_time:
        # 不跨日情况
        return start_time <= time_to_check <= end_time
    else:
        # 跨日情况
        return time_to_check >= start_time or time_to_check <= end_time
    
# 添加更新实际接待数的辅助函数
async def update_actual_reception_count(db: Session, current_date: date, channel_type: str, member: str, queue_type: str, is_transferred: bool = False, original_queue_type: str = None):
    # return {
    #     "current_date":current_date,
    #     "channel_type":channel_type,
    #     "member":member,
    #     "queue_type":queue_type,
    #     "is_transferred":is_transferred,
    #     "original_queue_type":original_queue_type,
    #     }
    """
    更新成员和渠道的实际接待数 
    参数:
        db: 数据库会话
        current_date: 当前日期
        channel_type: 渠道类型 (电商渠道/新媒体渠道)
        member: 成员名称
        queue_type: 队列类型 (free/paid)
        is_transferred: 是否是队列转移情况
        original_queue_type: 原始队列类型，在队列转移情况下使用
    
    返回:
        成功更新返回True，失败返回False
    """
    current_date_now = datetime.now()
    try:
        # 查找该成员当天的分发规则
        rule = db.query(DistributionRule).filter(
            DistributionRule.date == current_date,
            DistributionRule.channel_type == channel_type,
            DistributionRule.member == member,
            current_date_now >= DistributionRule.time_range_start,
            current_date_now <= DistributionRule.time_range_end,
        ).first()

        if rule:
            # 获取当前的实际接待数
            current_total = rule.actual_total or 0
            current_free = rule.actual_free or 0
            current_paid = rule.actual_paid or 0
            
            # 更新对应字段
            
            
            # 确定应该更新哪个字段
            update_queue_type = original_queue_type if is_transferred else queue_type
            # 根据队列类型更新免费/付费接待数
            if update_queue_type == "free":
                    rule.actual_free = current_free + 1
                    print(f"队列转移情况下，更新免费接待数: {current_free} -> {rule.actual_free}")
            else:  # paid
                    rule.actual_paid = current_paid + 1
                    print(f"队列转移情况下，更新付费接待数: {current_paid} -> {rule.actual_paid}")
           
            rule.actual_total = current_total + 1
            
            # 同时更新渠道总接待数
            channel_reception = db.query(ChannelReception).filter(
                ChannelReception.rule_date == current_date,
                ChannelReception.channel_type == channel_type
            ).first()

            if channel_reception:
                # 获取当前渠道的实际接待数
                channel_total = channel_reception.actual_reception or 0
                channel_free = channel_reception.actual_free_reception or 0
                channel_paid = channel_reception.actual_paid_reception or 0
                
               
            # 根据队列类型更新免费/付费接待数
            if update_queue_type == "free":
                channel_reception.actual_free_reception = channel_free + 1
                print(f"队列转移情况下，更新渠道免费接待数: {channel_free} -> {channel_reception.actual_free_reception}")
            else:  # paid
                channel_reception.actual_paid_reception = channel_paid + 1
                print(f"队列转移情况下，更新渠道付费接待数: {channel_paid} -> {channel_reception.actual_paid_reception}")
           
            # 更新渠道总接待数
            channel_reception.actual_reception = channel_total + 1
            print(f"成功更新 {member} 的实际接待数：总数={rule.actual_total}, 类型={update_queue_type} {'(原始类型)' if is_transferred else ''}")
            return True
        else:
            print(f"未找到成员 {member} 在 {current_date} 的分发规则")
            return False
    except Exception as e:
        print(f"更新实际接待数时出错: {e}")
        return False


#查询分配的线索发送邮箱
@router.get("/api/distribution_send_email")
async def distribution_send_email(db: Session = Depends(get_db)):

    #查询销售部所有人的真实姓名和邮箱，并以健值对表现出来
    user_xiaoshoubu_arr = db.query(User).filter(User.department == "销售部").all()
    # 构造键值对
    user_xiaoshoubu_team = {user.name: user.contact for user in user_xiaoshoubu_arr}
    #将邮箱解密
    if user_xiaoshoubu_team:
        for k, v in user_xiaoshoubu_team.items():
            if isinstance(v, str) :
                user_xiaoshoubu_team[k] = base64_decrypt_data(v)


    current_date = datetime.now().date()
    # 按成员分组统计已分发数量
    members_arr = [members for members, in db.query(DistributionQueue.member).filter(
         DistributionQueue.queue_date == current_date,
         DistributionQueue.email_seeded == 0,
         DistributionQueue.status == "distributed"
    ).group_by(DistributionQueue.member).order_by(DistributionQueue.position.desc()).all()]

    if members_arr:
        for members in members_arr:
            if user_xiaoshoubu_team[members]:
                aoksend_send_email(user_xiaoshoubu_team[members])

        res_email = db.query(DistributionQueue).filter(
            DistributionQueue.queue_date == current_date,
            DistributionQueue.email_seeded == 0,
            DistributionQueue.status == "distributed"
        ).update({DistributionQueue.email_seeded: 1 }, synchronize_session=False)
        db.commit()
    if res_email>0:
        return {"success": True,"message": "邮件发送成功","count": res_email}
    return {"success": False,"message": "邮件发送失败","count": res_email}


@router.get("/api/distribution/rules")
def get_distribution_rules(
    rule_date: date = Query(None),
    channel_type: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    
    """
    获取分发规则列表并更新实际接待数量统计信息
    新增功能（v2.1）：
    - 新增对字段 actual_total、actual_free 和 actual_paid 的统计计算
    - 统计依据为 ClueSheet 与 SalaCrm 的联表查询
    """
    try:
        # 基础查询，过滤掉已删除的记录
        query = db.query(DistributionRule).filter(DistributionRule.is_deleted == 0)

        if rule_date:
            query = query.filter(DistributionRule.date == rule_date)
        
        if channel_type:
            query = query.filter(DistributionRule.channel_type == channel_type)
        
        rules = query.order_by(DistributionRule.channel_type, DistributionRule.group_name).all()
        return rules
        # 批量更新实际接待数统计
        update_actual_reception_statistics(db, rules)
        # 重新查询以获取更新后的数据
        updated_rules = query.order_by(DistributionRule.channel_type, DistributionRule.group_name).all()

        print(f"成功获取并更新 {len(updated_rules)} 条分发规则的实际接待数统计")
        return updated_rules
        
    except Exception as e:
        print(f"获取分发规则失败: {e}")
        import traceback
        print(f"堆栈跟踪: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"获取分发规则失败: {str(e)}")



def update_actual_reception_statistics(db: Session, rules: List[DistributionRule]):
    """
    批量更新分发规则的实际接待数统计
    更新字段定义：
    - actual_total: ClueSheet.status ∈ [3,4]，DistributionRule.date = SalaCrm.allocation_date，DistributionRule.member = ClueSheet.contact_person
    - actual_free: 同上 + ClueSheet.queue = 0  
    - actual_paid: 同上 + ClueSheet.queue = 1
    ionRule.date == SalaCrm.allocation_date 为通用时间条件
    """
    try:

        # 使用事务确保数据一致性
        for rule in rules:

            # 基础查询条件：联表查询ClueSheet和SalaCrm
            # 根据用户需求，使用ClueSheet.ID == SalaCrm.ClueID进行关联

            base_query = db.query(ClueSheet).join(
                SalaCrm, ClueSheet.ID == SalaCrm.ID
            ).filter(
                and_(
                    ClueSheet.status.in_([3, 4]),  # 状态为3或4
                    func.DATE(SalaCrm.allocation_date) == rule.date,  # 日期匹配：DistributionRule.date = SalaCrm.allocation_date（使用DATE函数比较日期部分）
                    ClueSheet.contact_person == rule.member,  # 成员匹配：DistributionRule.member = ClueSheet.contact_person
                    ClueSheet.is_deleted == 0  # 未删除的线索
                )
            )

            # 计算实际接待总数
            actual_total = base_query.count()
            
            # 计算免费接待数 (queue = 0)
            actual_free = base_query.filter(ClueSheet.queue == '1').count()
            
            # 计算付费接待数 (queue = 1)  
            actual_paid = base_query.filter(ClueSheet.queue == '0').count()

            # 更新分发规则记录
            rule.actual_total = actual_total
            rule.actual_free = actual_free
            rule.actual_paid = actual_paid
            rule.updated_at = datetime.now()
            print(f"更新规则ID {rule.id}({rule.member}, {rule.date}): total={actual_total}, free={actual_free}, paid={actual_paid}")
        
        # 提交事务
        db.commit()
        print(f"成功更新 {len(rules)} 条分发规则的实际接待数统计")
        
    except Exception as e:
        # 回滚事务
        db.rollback()
        print(f"更新实际接待数统计失败: {e}")
        import traceback
        print(f"堆栈跟踪: {traceback.format_exc()}")
        raise e


# 队列更新请求模型
class QueueUpdateRequest(BaseModel):
    queue_date: date
    channel_type: str

def detect_member_replacements(existing_queue_items, current_rules, channel_type):
    """
    检测人员替换场景

    人员替换的特征：
    1. 现有队列中存在某个成员A的数据
    2. 新规则中不再包含成员A，但包含新成员B
    3. 成员A和成员B在相同时间段有相同的队列数量

    参数：
        existing_queue_items: 现有队列项列表
        current_rules: 当前分发规则列表
        channel_type: 渠道类型

    返回：
        人员替换列表，每个元素包含：
        - old_member: 被替换的成员
        - new_member: 新成员
        - time_slot: 时间段
        - queue_counts: 队列数量 {"free": x, "paid": y}
    """
    # 统计现有队列中的成员+时间段组合
    existing_member_time_stats = {}
    for item in existing_queue_items:
        key = f"{item.member}_{item.time_slot}"
        if key not in existing_member_time_stats:
            existing_member_time_stats[key] = {"free": 0, "paid": 0}
        existing_member_time_stats[key][item.queue_type] += 1

    # 统计新规则中的成员+时间段组合
    new_member_time_stats = {}
    for rule in current_rules:
        member = rule.member
        time_slot = rule.time_range or "09:00-23:59"
        key = f"{member}_{time_slot}"
        new_member_time_stats[key] = {
            "free": rule.expected_free or 0,
            "paid": rule.expected_paid or 0
        }

    # 提取成员列表
    existing_members = set()
    new_members = set()

    for key in existing_member_time_stats.keys():
        member = key.split('_')[0]
        existing_members.add(member)

    for key in new_member_time_stats.keys():
        member = key.split('_')[0]
        new_members.add(member)

    # 找出被删除的成员和新增的成员
    removed_members = existing_members - new_members
    added_members = new_members - existing_members

    print(f"人员替换检测 - {channel_type}:")
    print(f"  现有成员: {existing_members}")
    print(f"  新规则成员: {new_members}")
    print(f"  被删除成员: {removed_members}")
    print(f"  新增成员: {added_members}")

    # 检测人员替换：寻找数量匹配的删除+新增组合
    member_replacements = []

    for removed_member in removed_members:
        for added_member in added_members:
            # 检查是否存在时间段和数量都匹配的情况
            for existing_key, existing_counts in existing_member_time_stats.items():
                if not existing_key.startswith(f"{removed_member}_"):
                    continue

                time_slot = existing_key.split('_', 1)[1]
                new_key = f"{added_member}_{time_slot}"

                if new_key in new_member_time_stats:
                    new_counts = new_member_time_stats[new_key]

                    # 如果数量完全匹配，认为是人员替换
                    if (existing_counts["free"] == new_counts["free"] and
                        existing_counts["paid"] == new_counts["paid"]):

                        member_replacements.append({
                            "old_member": removed_member,
                            "new_member": added_member,
                            "time_slot": time_slot,
                            "queue_counts": existing_counts.copy()
                        })

                        print(f"  检测到人员替换: {removed_member} -> {added_member} (时间段: {time_slot}, 免费: {existing_counts['free']}, 付费: {existing_counts['paid']})")

    return member_replacements


def process_member_replacements(member_replacements, queue_date, channel_type, db):
    """
    处理人员替换场景

    将被替换人员的所有队列项的member字段更新为新人员

    参数：
        member_replacements: 人员替换列表
        queue_date: 队列日期
        channel_type: 渠道类型
        db: 数据库会话

    返回：
        处理结果字典 {"success": bool, "message": str, "updated_count": int}
    """
    try:
        updated_count = 0

        for replacement in member_replacements:
            old_member = replacement["old_member"]
            new_member = replacement["new_member"]
            time_slot = replacement["time_slot"]

            print(f"处理人员替换: {old_member} -> {new_member} (时间段: {time_slot})")

            # 查找被替换人员的所有队列项
            old_member_items = db.query(DistributionQueue).filter(
                DistributionQueue.queue_date == queue_date,
                DistributionQueue.channel_type == channel_type,
                DistributionQueue.member == old_member,
                DistributionQueue.time_slot == time_slot
            ).all()

            # 检查是否有已分发的队列项
            distributed_items = [item for item in old_member_items if item.status == "distributed"]
            pending_items = [item for item in old_member_items if item.status == "pending"]

            if distributed_items:
                print(f"  警告: {old_member} 有 {len(distributed_items)} 个已分发队列项，将保持不变")
                print(f"  只更新 {len(pending_items)} 个待分发队列项")
                items_to_update = pending_items
            else:
                print(f"  更新 {old_member} 的所有 {len(old_member_items)} 个队列项")
                items_to_update = old_member_items

            # 更新队列项的成员信息
            for item in items_to_update:
                old_member_name = item.member
                item.member = new_member
                item.updated_at = datetime.now()
                updated_count += 1
                print(f"    更新队列项ID {item.id}: {old_member_name} -> {new_member} (position: {item.position}, status: {item.status})")

        # 提交更新
        db.commit()

        message = f"成功处理 {len(member_replacements)} 个人员替换，更新了 {updated_count} 个队列项"
        print(message)

        return {
            "success": True,
            "message": message,
            "updated_count": updated_count
        }

    except Exception as e:
        db.rollback()
        error_msg = f"处理人员替换失败: {str(e)}"
        print(error_msg)
        import traceback
        print(f"堆栈跟踪: {traceback.format_exc()}")

        return {
            "success": False,
            "message": error_msg,
            "updated_count": 0
        }


def detect_queue_changes(queue_date: date, channel_type: str, db: Session):
    """
    检测队列是否需要更新

    比较当前分发规则与已保存队列的差异，确定是否需要更新
    支持检测人员替换场景

    返回：
    - needs_update: 是否需要更新
    - change_summary: 变更摘要
    - rule_changes: 详细的规则变更信息
    - member_replacements: 人员替换列表
    """
    try:
        from models import DistributionRule

        # 获取当前的分发规则
        current_rules = db.query(DistributionRule).filter(
            DistributionRule.date == queue_date,
            DistributionRule.channel_type == channel_type
        ).all()

        if not current_rules:
            return {
                "needs_update": False,
                "change_summary": f"未找到 {queue_date} {channel_type} 的分发规则",
                "rule_changes": []
            }

        # 获取现有队列项
        existing_queue_items = db.query(DistributionQueue).filter(
            DistributionQueue.queue_date == queue_date,
            DistributionQueue.channel_type == channel_type
        ).all()

        # 按成员+时间段统计现有队列数量
        existing_stats = {}
        for item in existing_queue_items:
            key = f"{item.member}_{item.time_slot}"
            if key not in existing_stats:
                existing_stats[key] = {"free": 0, "paid": 0}
            existing_stats[key][item.queue_type] += 1

        # 比较规则与现有队列的差异
        rule_changes = []
        needs_update = False

        for rule in current_rules:
            member = rule.member
            time_slot = rule.time_range or "09:00-23:59"
            key = f"{member}_{time_slot}"

            expected_free = rule.expected_free or 0
            expected_paid = rule.expected_paid or 0

            existing_free = existing_stats.get(key, {}).get("free", 0)
            existing_paid = existing_stats.get(key, {}).get("paid", 0)

            # 检查是否有变化
            if expected_free != existing_free or expected_paid != existing_paid:
                needs_update = True
                rule_changes.append({
                    "member": member,
                    "time_slot": time_slot,
                    "expected_free": expected_free,
                    "existing_free": existing_free,
                    "expected_paid": expected_paid,
                    "existing_paid": existing_paid,
                    "free_diff": expected_free - existing_free,
                    "paid_diff": expected_paid - existing_paid
                })

        # 检测人员替换场景
        member_replacements = detect_member_replacements(existing_queue_items, current_rules, channel_type)

        # 如果检测到人员替换，也需要更新
        if member_replacements:
            needs_update = True

        # 更新变更摘要
        replacement_summary = f"，检测到 {len(member_replacements)} 个人员替换" if member_replacements else ""
        change_summary = f"检测到 {len(rule_changes)} 个成员的队列需要调整{replacement_summary}" if needs_update else "队列无需更新"

        return {
            "needs_update": needs_update,
            "change_summary": change_summary,
            "rule_changes": rule_changes,
            "member_replacements": member_replacements
        }

    except Exception as e:
        print(f"检测队列变更失败: {e}")
        return {
            "needs_update": True,  # 出错时默认需要更新
            "change_summary": f"检测失败: {str(e)}",
            "rule_changes": [],
            "member_replacements": []
        }


@router.put("/api/distribution/queue/update")
def update_distribution_queue(
    request: Request,
    req_data: QueueUpdateRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    智能更新分发队列（增强版 - 支持变更检测和渠道隔离）

    实现队列更新逻辑规则：
    1. 渠道隔离：只更新指定渠道的队列，不影响其他渠道
    2. 变更检测：先检测是否真的需要更新，避免不必要的操作
    3. 保持已分发队列不变：所有状态为"已分发"的队列记录必须保持原有的序号和分配关系
    4. 队列增加场景：在原有队列基础上新增队列项，使用交叉排列算法分配
    5. 队列减少场景：不减少已分发队列，仅为需要增加的成员分配新的待分发队列
    6. 立即应用时间段交叉排序并返回结果

    架构优势：
    - 所有队列更新逻辑在后端处理，确保数据一致性
    - 前端只负责接收和显示后端返回的数据
    - 避免前后端数据同步问题
    - 渠道间完全隔离，避免跨渠道数据干扰
    """
    try:
        # 获取当前用户
        current_user = get_current_user(request, db)
        if not current_user:
            raise HTTPException(status_code=401, detail="用户未登录")

        print(f"开始更新队列: {req_data.queue_date} {req_data.channel_type}")

        # 1. 变更检测 - 检查是否真的需要更新
        change_detection = detect_queue_changes(req_data.queue_date, req_data.channel_type, db)
        print(f"变更检测结果: {change_detection['change_summary']}")

        if not change_detection["needs_update"]:
            print("队列无需更新，直接返回现有数据")
            # 直接返回现有队列数据（应用排序）
            return get_saved_distribution_queue(req_data.queue_date, req_data.channel_type, db)

        # 1.1. 处理人员替换场景
        member_replacements = change_detection.get("member_replacements", [])
        if member_replacements:
            print(f"检测到 {len(member_replacements)} 个人员替换，开始处理...")
            replacement_result = process_member_replacements(member_replacements, req_data.queue_date, req_data.channel_type, db)
            if replacement_result["success"]:
                print("人员替换处理完成，返回更新后的队列数据")
                return get_saved_distribution_queue(req_data.queue_date, req_data.channel_type, db)
            else:
                print(f"人员替换处理失败: {replacement_result['message']}")
                # 继续执行常规更新逻辑

        # 2. 渠道隔离验证 - 确保只操作指定渠道的数据
        print(f"渠道隔离验证: 只处理 {req_data.channel_type} 的数据")

        # 2.1. 获取现有的已分发队列项（必须保持不变）
        existing_distributed_items = db.query(DistributionQueue).filter(
            DistributionQueue.queue_date == req_data.queue_date,
            DistributionQueue.channel_type == req_data.channel_type,
            DistributionQueue.status == "distributed"
        ).all()

        print(f"找到 {req_data.channel_type} 已分发队列项: {len(existing_distributed_items)}条")

        # 2.2. 获取所有现有队列项（已分发 + 待分发）用于增量计算
        all_existing_items = db.query(DistributionQueue).filter(
            DistributionQueue.queue_date == req_data.queue_date,
            DistributionQueue.channel_type == req_data.channel_type
        ).all()

        print(f"找到 {req_data.channel_type} 所有现有队列项: {len(all_existing_items)}条")

        # 3. 获取最新的分发规则（只获取指定渠道的规则）
        from models import DistributionRule
        rules = db.query(DistributionRule).filter(
            DistributionRule.date == req_data.queue_date,
            DistributionRule.channel_type == req_data.channel_type
        ).all()

        if not rules:
            raise HTTPException(status_code=404, detail=f"未找到 {req_data.queue_date} {req_data.channel_type} 的分发规则")

        print(f"获取到 {req_data.channel_type} 分发规则: {len(rules)}条")

        # 3.1. 验证规则变更详情
        if change_detection["rule_changes"]:
            print("检测到的规则变更详情:")
            for change in change_detection["rule_changes"]:
                print(f"  成员: {change['member']}, 时间段: {change['time_slot']}")
                print(f"    免费队列: {change['existing_free']} -> {change['expected_free']} (差值: {change['free_diff']})")
                print(f"    付费队列: {change['existing_paid']} -> {change['expected_paid']} (差值: {change['paid_diff']})")

        # 4. 按成员+时间段统计已分发数量（用于保护已分发队列）
        member_time_distributed_stats = {}
        for item in existing_distributed_items:
            member = item.member
            time_slot = item.time_slot
            queue_type = item.queue_type

            # 创建成员+时间段的唯一键
            key = f"{member}_{time_slot}"
            if key not in member_time_distributed_stats:
                member_time_distributed_stats[key] = {"free": 0, "paid": 0}
            member_time_distributed_stats[key][queue_type] += 1

        print(f"{req_data.channel_type} 成员按时间段已分发统计: {member_time_distributed_stats}")

        # 4.1. 按成员+时间段统计所有现有队列数量（用于增量计算）
        member_time_total_stats = {}
        for item in all_existing_items:
            member = item.member
            time_slot = item.time_slot
            queue_type = item.queue_type

            # 创建成员+时间段的唯一键
            key = f"{member}_{time_slot}"
            if key not in member_time_total_stats:
                member_time_total_stats[key] = {"free": 0, "paid": 0}
            member_time_total_stats[key][queue_type] += 1

        print(f"{req_data.channel_type} 成员按时间段现有队列总数统计: {member_time_total_stats}")

        # 5. 计算每个成员每个班次需要新增的队列数量（基于现有总数进行增量计算）
        new_queue_items = []

        for rule in rules:
            member = rule.member
            time_slot = rule.time_range or "09:00-23:59"

            # 获取该成员该时间段的已分发数量（用于保护已分发队列）
            key = f"{member}_{time_slot}"
            distributed_stats = member_time_distributed_stats.get(key, {"free": 0, "paid": 0})

            # 获取该成员该时间段的现有队列总数（用于增量计算）
            total_stats = member_time_total_stats.get(key, {"free": 0, "paid": 0})

            # 计算新规则的预期数量
            expected_free = rule.expected_free or 0
            expected_paid = rule.expected_paid or 0

            # 基于现有总数计算增量（新规则量 - 现有总数，最小为0）
            need_free = max(expected_free - total_stats["free"], 0)
            need_paid = max(expected_paid - total_stats["paid"], 0)

            print(f"{req_data.channel_type} 成员 {member} 时间段 {time_slot}:")
            print(f"  预期免费={expected_free}, 现有免费={total_stats['free']}, 已分发免费={distributed_stats['free']}, 需新增免费={need_free}")
            print(f"  预期付费={expected_paid}, 现有付费={total_stats['paid']}, 已分发付费={distributed_stats['paid']}, 需新增付费={need_paid}")

            # 验证：确保不会减少已分发的队列
            if expected_free < distributed_stats["free"]:
                print(f"  警告：{req_data.channel_type} 成员 {member} 时间段 {time_slot} 的免费队列新规则({expected_free})小于已分发数量({distributed_stats['free']})，将保持已分发队列不变")
            if expected_paid < distributed_stats["paid"]:
                print(f"  警告：{req_data.channel_type} 成员 {member} 时间段 {time_slot} 的付费队列新规则({expected_paid})小于已分发数量({distributed_stats['paid']})，将保持已分发队列不变")

            # 生成新的队列项（只为当前渠道生成）
            for _ in range(need_free):
                new_queue_items.append({
                    "queue_type": "free",
                    "group_name": rule.group_name or "",
                    "leader": rule.leader or "",
                    "member": member,
                    "time_slot": rule.time_range or "09:00-23:59",
                    "status": "pending",
                    "channel_type": req_data.channel_type  # 明确标记渠道类型
                })

            for _ in range(need_paid):
                new_queue_items.append({
                    "queue_type": "paid",
                    "group_name": rule.group_name or "",
                    "leader": rule.leader or "",
                    "member": member,
                    "time_slot": rule.time_range or "09:00-23:59",
                    "status": "pending",
                    "channel_type": req_data.channel_type  # 明确标记渠道类型
                })

        print(f"{req_data.channel_type} 生成新队列项: {len(new_queue_items)}条")

        # 4.1. 处理队列减少场景：删除多余的待分发队列项
        items_to_remove = []

        for rule in rules:
            member = rule.member
            time_slot = rule.time_range or "09:00-23:59"
            key = f"{member}_{time_slot}"

            # 获取统计数据
            distributed_stats = member_time_distributed_stats.get(key, {"free": 0, "paid": 0})
            total_stats = member_time_total_stats.get(key, {"free": 0, "paid": 0})

            # 计算新规则的预期数量
            expected_free = rule.expected_free or 0
            expected_paid = rule.expected_paid or 0

            # 计算需要删除的数量（现有总数 - 新规则量，但不能少于已分发数量）
            target_free = max(expected_free, distributed_stats["free"])  # 至少保留已分发的数量
            target_paid = max(expected_paid, distributed_stats["paid"])  # 至少保留已分发的数量

            remove_free = max(total_stats["free"] - target_free, 0)
            remove_paid = max(total_stats["paid"] - target_paid, 0)

            if remove_free > 0 or remove_paid > 0:
                print(f"{req_data.channel_type} 成员 {member} 时间段 {time_slot}: 需要删除 免费={remove_free}条, 付费={remove_paid}条")

                # 查找该成员该时间段的待分发队列项（优先删除待分发的）
                pending_items = db.query(DistributionQueue).filter(
                    DistributionQueue.queue_date == req_data.queue_date,
                    DistributionQueue.channel_type == req_data.channel_type,
                    DistributionQueue.member == member,
                    DistributionQueue.time_slot == time_slot,
                    DistributionQueue.status != "distributed"  # 只删除未分发的
                ).order_by(DistributionQueue.position.desc()).all()  # 从后往前删除

                # 分别处理免费和付费队列的删除
                free_pending = [item for item in pending_items if item.queue_type == "free"]
                paid_pending = [item for item in pending_items if item.queue_type == "paid"]

                # 删除多余的免费队列项
                for i in range(min(remove_free, len(free_pending))):
                    items_to_remove.append(free_pending[i])
                    print(f"  标记删除免费队列项: ID={free_pending[i].id}, position={free_pending[i].position}")

                # 删除多余的付费队列项
                for i in range(min(remove_paid, len(paid_pending))):
                    items_to_remove.append(paid_pending[i])
                    print(f"  标记删除付费队列项: ID={paid_pending[i].id}, position={paid_pending[i].position}")

        # 执行删除操作
        if items_to_remove:
            print(f"{req_data.channel_type} 删除多余的队列项: {len(items_to_remove)}条")
            for item in items_to_remove:
                db.delete(item)
            db.commit()  # 先提交删除操作

        # 5. 如果没有新队列项且没有删除操作，直接返回现有队列（应用保护性排序）
        if not new_queue_items and not items_to_remove:
            print(f"{req_data.channel_type} 没有队列变更，应用保护性排序现有队列")

            # 保护性排序现有队列（保护已分发队列）
            for queue_type in ["free", "paid"]:
                queue_items = db.query(DistributionQueue).filter(
                    DistributionQueue.queue_date == req_data.queue_date,
                    DistributionQueue.channel_type == req_data.channel_type,
                    DistributionQueue.queue_type == queue_type
                ).all()

                if queue_items:
                    # 使用保护已分发队列的智能排序算法
                    sort_queue_with_distributed_protection(queue_items, db, req_data.queue_date, req_data.channel_type)
                    # 注意：新算法内部已经处理了position分配

            db.commit()

            # 返回排序后的队列数据
            return get_saved_distribution_queue(req_data.queue_date, req_data.channel_type, db)

        # 6. 如果有删除操作但没有新增，应用保护性排序并返回
        if items_to_remove and not new_queue_items:
            print(f"{req_data.channel_type} 只有删除操作，保护性排序剩余队列")

            # 保护性排序剩余队列（保护已分发队列）
            for queue_type in ["free", "paid"]:
                queue_items = db.query(DistributionQueue).filter(
                    DistributionQueue.queue_date == req_data.queue_date,
                    DistributionQueue.channel_type == req_data.channel_type,
                    DistributionQueue.queue_type == queue_type
                ).all()

                if queue_items:
                    # 使用保护已分发队列的智能排序算法
                    sort_queue_with_distributed_protection(queue_items, db, req_data.queue_date, req_data.channel_type)
                    # 注意：新算法内部已经处理了position分配

            db.commit()

            # 返回排序后的队列数据
            return get_saved_distribution_queue(req_data.queue_date, req_data.channel_type, db)

        # 7. 使用增量添加函数（完全跳过排序，保持现有队列顺序不变）
        added_count = add_queue_items_incrementally(new_queue_items, req_data, db)

        # 提交新增的队列项
        db.commit()
        print(f"{req_data.channel_type} 增量更新完成: 成功添加{added_count}个新队列项，保持所有现有队列顺序不变")

        # 9. 启动后台任务处理待分发线索
        background_tasks.add_task(process_pending_leads_background, db)

        # 10. 返回更新后的队列数据（已应用交叉排序）
        result = get_saved_distribution_queue(req_data.queue_date, req_data.channel_type, db)

        print(f"{req_data.channel_type} 队列更新完成: 免费队列{len(result['free_queue'])}项, 付费队列{len(result['paid_queue'])}项")

        return {
            "success": True,
            "message": f"成功更新 {req_data.channel_type} 分发队列，已应用保护性排序和渠道隔离（已分发队列保持不变）",
            "queue_date": req_data.queue_date,
            "channel_type": req_data.channel_type,
            "free_queue": result["free_queue"],
            "paid_queue": result["paid_queue"],
            "total_free": len(result["free_queue"]),
            "total_paid": len(result["paid_queue"]),
            "total_items": len(result["free_queue"]) + len(result["paid_queue"]),
            "algorithm_description": "智能队列更新算法（保护版）：支持渠道隔离、变更检测，严格保护已分发队列不变，根据新规则智能计算需要新增的队列项，应用保护性排序确保已分发队列完整性，避免序号混乱问题。",
            "change_detection": change_detection
        }

    except Exception as e:
        db.rollback()
        print(f"更新队列失败: {e}")
        import traceback
        print(f"堆栈跟踪: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"更新队列失败: {str(e)}")


@router.get("/api/distribution/queue/changes")
def check_queue_changes(
    queue_date: date = Query(..., description="队列日期"),
    channel_type: str = Query(..., description="渠道类型"),
    db: Session = Depends(get_db)
):
    """
    检测队列变更API

    用于前端在更新队列前检查是否真的需要更新，避免不必要的操作
    """
    try:
        change_detection = detect_queue_changes(queue_date, channel_type, db)

        return {
            "success": True,
            "queue_date": queue_date,
            "channel_type": channel_type,
            "needs_update": change_detection["needs_update"],
            "change_summary": change_detection["change_summary"],
            "rule_changes": change_detection["rule_changes"],
            "total_changes": len(change_detection["rule_changes"])
        }

    except Exception as e:
        print(f"检测队列变更失败: {e}")
        raise HTTPException(status_code=500, detail=f"检测队列变更失败: {str(e)}")


def validate_channel_isolation(queue_date: date, channel_type: str, db: Session):
    """
    验证渠道隔离的数据一致性

    确保指定渠道的操作不会影响其他渠道的数据
    """
    try:
        # 获取所有渠道的队列统计
        all_channels = ["电商渠道", "新媒体渠道"]
        channel_stats = {}

        for ch in all_channels:
            free_count = db.query(func.count(DistributionQueue.id)).filter(
                DistributionQueue.queue_date == queue_date,
                DistributionQueue.channel_type == ch,
                DistributionQueue.queue_type == "free"
            ).scalar() or 0

            paid_count = db.query(func.count(DistributionQueue.id)).filter(
                DistributionQueue.queue_date == queue_date,
                DistributionQueue.channel_type == ch,
                DistributionQueue.queue_type == "paid"
            ).scalar() or 0

            distributed_count = db.query(func.count(DistributionQueue.id)).filter(
                DistributionQueue.queue_date == queue_date,
                DistributionQueue.channel_type == ch,
                DistributionQueue.status == "distributed"
            ).scalar() or 0

            channel_stats[ch] = {
                "free_count": free_count,
                "paid_count": paid_count,
                "total_count": free_count + paid_count,
                "distributed_count": distributed_count
            }

        return {
            "validation_passed": True,
            "channel_stats": channel_stats,
            "target_channel": channel_type,
            "validation_time": datetime.now().isoformat()
        }

    except Exception as e:
        print(f"渠道隔离验证失败: {e}")
        return {
            "validation_passed": False,
            "error": str(e),
            "validation_time": datetime.now().isoformat()
        }


@router.get("/api/distribution/queue/validation")
def validate_queue_integrity(
    queue_date: date = Query(..., description="队列日期"),
    channel_type: str = Query(..., description="渠道类型"),
    db: Session = Depends(get_db)
):
    """
    验证队列数据完整性API

    检查渠道隔离和数据一致性
    """
    try:
        validation_result = validate_channel_isolation(queue_date, channel_type, db)

        return {
            "success": True,
            "queue_date": queue_date,
            "channel_type": channel_type,
            "validation_result": validation_result
        }

    except Exception as e:
        print(f"验证队列完整性失败: {e}")
        return HTTPException(status_code=500, detail=f"验证队列完整性失败: {str(e)}")


# 队列分发算法配置
class QueueAlgorithmConfig(BaseModel):
    algorithm_type: str = "polling"  # "polling" 或 "random"
    description: Optional[str] = None

# 全局算法配置（可以存储在数据库或配置文件中）
QUEUE_ALGORITHM_CONFIG = {
    "current_algorithm": "time_slot_polling",  # 默认使用新的时间段轮询算法
    "available_algorithms": {
        "time_slot_polling": {
            "name": "时间段轮询算法",
            "description": "基于时间段的轮询分发算法：按排班开始时间（time_slot_beg）升序排序人员，执行甲→丁→乙→丙轮询分配，确保时间优先级和公平分配，避免时间段聚集问题。",
            "function": time_slot_based_polling_algorithm
        },
        "polling": {
            "name": "传统轮询算法",
            "description": "基于时间优先级和轮询分配的公平算法，在相同时间段内按A→B→C→A循环分配，跨时间段保持轮询连续性，确保分配的平衡性和可预测性。",
            "function": sort_queue_with_polling_algorithm
        },
        "random": {
            "name": "随机算法",
            "description": "完全随机打乱队列项，避免同一人连续分发的问题，消除时间段聚集造成的分发不均，确保分发队列的完全随机性。",
            "function": sort_queue_with_random_algorithm
        }
    }
}

@router.get("/api/distribution/queue/algorithm-config")
def get_queue_algorithm_config():
    """
    获取队列分发算法配置

    返回当前使用的算法和可用的算法列表
    """
    try:
        current_algorithm = QUEUE_ALGORITHM_CONFIG["current_algorithm"]
        available_algorithms = QUEUE_ALGORITHM_CONFIG["available_algorithms"]

        return {
            "success": True,
            "current_algorithm": current_algorithm,
            "current_algorithm_info": available_algorithms[current_algorithm],
            "available_algorithms": {
                key: {
                    "name": value["name"],
                    "description": value["description"]
                }
                for key, value in available_algorithms.items()
            }
        }
    except Exception as e:
        print(f"获取算法配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取算法配置失败: {str(e)}")

@router.post("/api/distribution/queue/algorithm-config")
def update_queue_algorithm_config(
    config: QueueAlgorithmConfig
):
    """
    更新队列分发算法配置

    允许在轮询算法和随机算法之间切换
    """
    try:
        available_algorithms = QUEUE_ALGORITHM_CONFIG["available_algorithms"]

        if config.algorithm_type not in available_algorithms:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的算法类型: {config.algorithm_type}。可用算法: {list(available_algorithms.keys())}"
            )

        # 更新全局配置
        QUEUE_ALGORITHM_CONFIG["current_algorithm"] = config.algorithm_type

        algorithm_info = available_algorithms[config.algorithm_type]

        print(f"队列分发算法已切换为: {algorithm_info['name']}")

        return {
            "success": True,
            "message": f"队列分发算法已成功切换为: {algorithm_info['name']}",
            "algorithm_type": config.algorithm_type,
            "algorithm_info": algorithm_info
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"更新算法配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新算法配置失败: {str(e)}")


def get_current_queue_algorithm():
    """
    获取当前配置的队列排序算法函数

    根据全局配置返回对应的算法函数
    """
    current_algorithm = QUEUE_ALGORITHM_CONFIG["current_algorithm"]
    available_algorithms = QUEUE_ALGORITHM_CONFIG["available_algorithms"]

    if current_algorithm in available_algorithms:
        return available_algorithms[current_algorithm]["function"]
    else:
        # 默认返回轮询算法
        print(f"警告: 未知的算法类型 {current_algorithm}，使用默认的轮询算法")
        return sort_queue_with_polling_algorithm

