from fastapi import APIRouter, HTTPException, Depends, Request
from sqlalchemy.orm import Session
from database import SessionLocal, get_db
from models import User
from sqlalchemy.exc import SQLAlchemyError
from auth import get_current_user,base64_decrypt_data
from models import UserPermissionBackend, UserPermissionFrontend
from typing import List, Dict, Any
from sqlalchemy import distinct, func
from fastapi.responses import RedirectResponse
from pydantic import BaseModel


router = APIRouter()

class CrmPermissionUpdate(BaseModel):
    permission_id: str
    permissions: Dict[str, int]

@router.get("/api/active-users")
async def get_active_users():
    try:
        db = SessionLocal()
        users = db.query(User).filter(User.is_admin != 3).all()
        
        user_list = [{
            "id": user.ID,
            "name": user.name,
            "department": user.department,
            "group_name": user.group_name,
            "register_time": user.created_at.isoformat(),
            "is_admin": user.is_admin
        } for user in users]
        
        return user_list
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"数据库错误: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")
    finally:
        db.close()

@router.delete("/api/users/{user_id}")
async def delete_user(user_id: str):
    try:
        db = SessionLocal()
        user = db.query(User).filter(User.ID == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        db.delete(user)
        db.commit()
        return {"message": "用户删除成功"}
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"数据库错误: {str(e)}")
    finally:
        db.close()

@router.get("/api/user/profile")
async def get_user_profile(request: Request, db: Session = Depends(get_db)):
    try:
        user = get_current_user(request, db)
        if not user:
            raise HTTPException(status_code=401, detail="用户未登录")
        
        return {
            "id": user.ID,
            "name": user.name,
            "department": user.department,
            "is_admin": user.is_admin,
            "identity": user.identity,
            "group_name": user.group_name,
            "contact":base64_decrypt_data(user.contact)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")

# 获取用户权限(后端权限，暂未修改函数名)
@router.get("/api/user-permissions-backend")
async def get_user_permissions():
    try:
        db = SessionLocal()
        # 导入所需模型
        from models import UserPermissionBackend
        
        # 查询所有用户权限数据，过滤掉身份为"超管"的记录
        permissions = db.query(UserPermissionBackend).filter(
            UserPermissionBackend.identity != '超管'
        ).all()
        
        permissions_list = [{
            "id": permission.id,
            "department": permission.department,
            "identity": permission.identity,
            "user_review": permission.user_review,
            "review_all": permission.review_all,
            "review_group": permission.review_group,
            "user_management": permission.user_management,
            "manage_all": permission.manage_all,
            "manage_group": permission.manage_group,
            "permission_manage": permission.permission_manage,
            "form_preset": permission.form_preset,
            "schedule_management": permission.schedule_management,
            "distribution_rules": permission.distribution_rules,
            "distribution_plan": permission.distribution_plan
        } for permission in permissions]
        
        return permissions_list
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"数据库错误: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")
    finally:
        db.close()

# 更新用户权限      
@router.post("/api/update-user-permissions-backend")
async def update_user_permissions_backend(permissions: List[dict], db: Session = Depends(get_db)):
    try:

        print(f"接收到权限更新请求，数据长度: {len(permissions)}")
        updated_count = 0
        created_count = 0
        frontend_created_count = 0
        
        # 获取当前表中的最大ID
        max_id_result = db.query(func.max(UserPermissionBackend.id)).first()
        current_max_id = max_id_result[0] if max_id_result[0] is not None else 0
        
        # 获取前端权限表的最大ID
        frontend_max_id_result = db.query(func.max(UserPermissionFrontend.id)).first()
        frontend_current_max_id = frontend_max_id_result[0] if frontend_max_id_result[0] is not None else 0

        for permission in permissions:
            perm_id = permission.get("id")
            is_new_record = isinstance(perm_id, str) and perm_id.startswith('temp_')
            
            # 权限字段列表
            permission_fields = [
                "user_review", "review_all", "review_group", 
                "user_management", "manage_all", "manage_group",
                "permission_manage", "form_preset", 
                "schedule_management", "distribution_rules", "distribution_plan"
            ]

            if is_new_record:
                try:
                    print(f"处理新增权限记录: {permission}")
                    # 创建新记录
                    new_permission = UserPermissionBackend(
                        department=permission.get("department"),
                        identity=permission.get("identity")
                    )
                    
                    # 验证部门和身份是否有值
                    if not new_permission.department or not new_permission.identity:
                        raise Exception(f"部门或身份字段不能为空: 部门={new_permission.department}, 身份={new_permission.identity}")
                    
                    # 获取序号并设置为id (这个序号是前端提交的，即前端显示的序号)
                    display_index = int(permission.get("display_index", 0))
                    if display_index > 0:
                        new_permission.id = display_index
                    else:
                        # 如果没有提供序号，则使用当前最大ID+1
                        current_max_id += 1
                        new_permission.id = current_max_id
                    
                    # 设置所有权限字段
                    for field in permission_fields:
                        setattr(new_permission, field, permission.get(field, 1))
                    
                    # 检查是否已存在相同ID的记录
                    existing = db.query(UserPermissionBackend).filter(UserPermissionBackend.id == new_permission.id).first()
                    if existing:
                        print(f"ID {new_permission.id} 已存在，自动递增ID")
                        # 查找下一个可用ID
                        next_id = new_permission.id
                        while True:
                            next_id += 1
                            check = db.query(UserPermissionBackend).filter(UserPermissionBackend.id == next_id).first()
                            if not check:
                                new_permission.id = next_id
                                break
                    
                    # 添加后端权限记录
                    db.add(new_permission)
                    db.flush()  # 提交但不提交事务，以获取生成的ID
                    created_count += 1
                    print(f"创建新权限记录: ID={new_permission.id}, 部门={permission.get('department')}, 身份={permission.get('identity')}")
                    
                    # 同步添加前端权限记录
                    # 检查是否已有相同部门和身份的前端权限记录
                    print(f"检查是否存在相同部门({new_permission.department})和身份({new_permission.identity})的前端权限记录")
                    existing_frontend = db.query(UserPermissionFrontend).filter(
                        UserPermissionFrontend.department == new_permission.department,
                        UserPermissionFrontend.identity == new_permission.identity
                    ).first()
                    
                    if not existing_frontend:
                        # 创建新的前端权限记录
                        frontend_max_id_result = db.query(func.max(UserPermissionFrontend.id)).first()
                        frontend_current_max_id = frontend_max_id_result[0] if frontend_max_id_result[0] is not None else 0
                        frontend_current_max_id += 1
                        
                        new_frontend_permission = UserPermissionFrontend(
                            id=frontend_current_max_id,
                            department=new_permission.department,
                            identity=new_permission.identity,
                            # 所有前端权限默认设为1 (禁止)
                            lead_table=1,
                            lead_submit=1,
                            recycle_bin=1,
                            user_messages=1,
                            check_all_lead=1,
                            check_member_lead=1,
                            check_mine_lead=1,
                            edit_all_lead=1,
                            edit_member_lead=1,
                            edit_mine_lead=1,
                            forbid_edit=1,
                            delete_all_lead=1,
                            delete_member_lead=1,
                            delete_mine_lead=1,
                            export_lead_table=1,
                            import_lead_table=1,
                            lead_crm=1,
                            check_person_crm=1,
                            check_group_crm=1,
                            check_all_crm=1,
                            check_all_bin=1,
                            check_member_bin=1,
                            check_mine_bin=1,
                            check_all_messages=1,
                            check_member_messages=1,
                            check_mine_messages=1,
                            check_shenpi=1,
                            check_all_shenpi=1,
                            check_mine_shenpi=1,
                            openseas_rule=1,
                            openseas=1,
                            openseas_all=1,
                            openseas_group=1,
                            openseas_mine=1
                        )
                        
                        # 检查是否已存在相同ID的记录
                        existing = db.query(UserPermissionFrontend).filter(UserPermissionFrontend.id == new_frontend_permission.id).first()
                        if existing:
                            print(f"前端权限表ID {new_frontend_permission.id} 已存在，自动递增ID")
                            # 查找下一个可用ID
                            next_id = new_frontend_permission.id
                            while True:
                                next_id += 1
                                check = db.query(UserPermissionFrontend).filter(UserPermissionFrontend.id == next_id).first()
                                if not check:
                                    new_frontend_permission.id = next_id
                                    break
                        
                        db.add(new_frontend_permission)
                        db.flush()
                        frontend_created_count += 1
                        print(f"同步创建前端权限记录: ID={new_frontend_permission.id}, 部门={new_permission.department}, 身份={new_permission.identity}")
                    else:
                        print(f"前端权限记录已存在: ID={existing_frontend.id}, 部门={existing_frontend.department}, 身份={existing_frontend.identity}, 跳过创建")
                        
                except Exception as e:
                    db.rollback()
                    print(f"创建新记录出错: {str(e)}")
                    import traceback
                    print(traceback.format_exc())
                    raise Exception(f"创建新记录出错: {str(e)}")
            else:
                try:
                    # 更新现有记录

                    user_permission = db.query(UserPermissionBackend).filter(UserPermissionBackend.id == perm_id).first()
                    #return user_permission
                    if not user_permission:
                        print(f"未找到ID={perm_id}的权限记录，跳过")
                        continue
                    
                    # 先获取旧的部门和身份，用于之后更新前端权限
                    old_department = user_permission.department
                    old_identity = user_permission.identity
                    
                    # 更新权限字段
                    for field in permission_fields:
                        if field in permission:
                            new_value = int(permission[field])  # 确保值为整数
                            old_value = getattr(user_permission, field)
                            if old_value != new_value:
                                print(f"更新ID={perm_id}的{field}: {old_value} -> {new_value}")
                                setattr(user_permission, field, new_value)
                    
                    # 更新身份和部门字段
                    new_department = permission.get("department")
                    new_identity = permission.get("identity")
                    
                    if new_department and user_permission.department != new_department:
                        print(f"更新ID={perm_id}的部门: {user_permission.department} -> {new_department}")
                        user_permission.department = new_department
                    
                    if new_identity and user_permission.identity != new_identity:
                        print(f"更新ID={perm_id}的身份: {user_permission.identity} -> {new_identity}")
                        user_permission.identity = new_identity
                    
                    updated_count += 1
                    
                    # 如果部门或身份发生了变化，也更新对应的前端权限记录
                    if new_department != old_department or new_identity != old_identity:
                        # 查找对应的前端权限记录
                        frontend_permission = db.query(UserPermissionFrontend).filter(
                            UserPermissionFrontend.department == old_department,
                            UserPermissionFrontend.identity == old_identity
                        ).first()
                        
                        if frontend_permission:
                            # 更新前端权限记录的部门和身份
                            frontend_permission.department = new_department if new_department else frontend_permission.department
                            frontend_permission.identity = new_identity if new_identity else frontend_permission.identity
                            print(f"同步更新前端权限记录: ID={frontend_permission.id}, 部门={frontend_permission.department}, 身份={frontend_permission.identity}")
                    
                except Exception as e:
                    db.rollback()
                    print(f"更新记录出错: {str(e)}")
                    import traceback
                    print(traceback.format_exc())
                    raise Exception(f"更新记录出错: {str(e)}")
        
        # 提交事务
        db.commit()
        print(f"成功更新了{updated_count}条权限记录，新增了{created_count}条权限记录，同步创建了{frontend_created_count}条前端权限记录")
        return {
            "status": "success", 
            "updated_count": updated_count,
            "created_count": created_count,
            "frontend_created_count": frontend_created_count,
            "message": f"成功更新了{updated_count}条权限记录，新增了{created_count}条权限记录，同步创建了{frontend_created_count}条前端权限记录"
        }
    
    except Exception as e:
        db.rollback()
        import traceback
        error_trace = traceback.format_exc()
        print(f"权限更新错误: {str(e)}")
        print(f"错误堆栈: {error_trace}")
        raise HTTPException(status_code=500, detail=f"更新权限失败: {str(e)}")

# 获取用户前端权限
@router.get("/api/user-permissions-frontend")
async def get_user_permissions_frontend():
    try:
        db = SessionLocal()
        # 导入所需模型
        from models import UserPermissionFrontend
        
        # 查询所有权限记录 - 基于部门和身份，而不是基于用户
        # 过滤掉身份为"超管"的记录
        permissions = db.query(UserPermissionFrontend).filter(
            UserPermissionFrontend.identity != '超管'
        ).all()
        
        permissions_list = [{
            "id": permission.id,  # 使用权限记录的ID
            "department": permission.department,
            "identity": permission.identity,
            "lead_table": permission.lead_table,
            "lead_submit": permission.lead_submit,
            "recycle_bin": permission.recycle_bin,
            "user_messages": permission.user_messages,
            "check_all_lead": permission.check_all_lead,
            "check_member_lead": permission.check_member_lead,
            "check_mine_lead": permission.check_mine_lead,
            "edit_all_lead": permission.edit_all_lead,
            "edit_member_lead": permission.edit_member_lead,
            "edit_mine_lead": permission.edit_mine_lead,
            "forbid_edit": permission.forbid_edit,
            "delete_all_lead": permission.delete_all_lead,
            "delete_member_lead": permission.delete_member_lead,
            "delete_mine_lead": permission.delete_mine_lead,
            "export_lead_table": permission.export_lead_table,
            "import_lead_table": permission.import_lead_table,
            "lead_crm": permission.lead_crm,
            "check_person_crm": permission.check_person_crm,
            "check_group_crm": permission.check_group_crm,
            "check_all_crm": permission.check_all_crm,
            "check_all_bin": permission.check_all_bin,
            "check_member_bin": permission.check_member_bin,
            "check_mine_bin": permission.check_mine_bin,
            "check_all_messages": permission.check_all_messages,
            "check_member_messages": permission.check_member_messages,
            "check_mine_messages": permission.check_mine_messages,
            "check_shenpi": permission.check_shenpi,
            "check_all_shenpi": permission.check_all_shenpi,
            "check_mine_shenpi": permission.check_mine_shenpi,
            "openseas_rule": permission.openseas_rule,
            "openseas": permission.openseas,
            "openseas_all": permission.openseas_all,
            "openseas_group": permission.openseas_group,
            "openseas_mine": permission.openseas_mine
        } for permission in permissions]
        
        return permissions_list
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"数据库错误: {str(e)}")
    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        print(f"获取前端权限错误: {str(e)}")
        print(f"错误堆栈: {error_trace}")
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")
    finally:
        db.close()

# 更新用户前端权限
@router.post("/api/update-user-permissions-frontend")
async def update_user_permissions_frontend(permissions: List[dict], db: Session = Depends(get_db)):
    try:
        from models import UserPermissionFrontend, UserPermissionBackend
        
        print(f"接收到前端权限更新请求，数据长度: {len(permissions)}")
        updated_count = 0
        created_count = 0
        backend_created_count = 0
        
        # 获取当前表中的最大ID
        max_id_result = db.query(func.max(UserPermissionFrontend.id)).first()
        current_max_id = max_id_result[0] if max_id_result[0] is not None else 0
        
        # 获取后端权限表的最大ID
        backend_max_id_result = db.query(func.max(UserPermissionBackend.id)).first()
        backend_current_max_id = backend_max_id_result[0] if backend_max_id_result[0] is not None else 0
        
        for permission in permissions:
            # 获取ID字段
            perm_id = permission.get("id")
            is_new_record = isinstance(perm_id, str) and perm_id.startswith('temp_')
            
            # 权限字段列表
            permission_fields = [
                "lead_table", "lead_submit", "recycle_bin", "user_messages",
                "check_all_lead", "check_member_lead", "check_mine_lead",
                "edit_all_lead", "edit_member_lead", "edit_mine_lead", "forbid_edit",
                "delete_all_lead", "delete_member_lead", "delete_mine_lead",
                "export_lead_table", "import_lead_table", "lead_crm",
                "check_person_crm", "check_group_crm", "check_all_crm",
                "check_all_bin", "check_member_bin", "check_mine_bin",
                "check_all_messages", "check_member_messages", "check_mine_messages",
                "check_shenpi","check_all_shenpi","check_mine_shenpi",
                "openseas_rule","openseas","openseas_all","openseas_group","openseas_mine"
            ]
            
            if is_new_record:
                try:
                    print(f"处理新增前端权限记录: {permission}")
                    # 创建新记录
                    new_permission = UserPermissionFrontend(
                        department=permission.get("department"),
                        identity=permission.get("identity")
                    )
                    
                    # 验证部门和身份是否有值
                    if not new_permission.department or not new_permission.identity:
                        raise Exception(f"部门或身份字段不能为空: 部门={new_permission.department}, 身份={new_permission.identity}")
                    
                    # 获取序号并设置为id (这个序号是前端提交的，即前端显示的序号)
                    display_index = int(permission.get("display_index", 0))
                    if display_index > 0:
                        new_permission.id = display_index
                    else:
                        # 如果没有提供序号，则使用当前最大ID+1
                        current_max_id += 1
                        new_permission.id = current_max_id
                    
                    # 设置所有权限字段
                    for field in permission_fields:
                        if field in permission:
                            setattr(new_permission, field, int(permission.get(field, 1)))
                    
                    # 检查是否已存在相同ID的记录
                    existing = db.query(UserPermissionFrontend).filter(UserPermissionFrontend.id == new_permission.id).first()
                    if existing:
                        print(f"ID {new_permission.id} 已存在，自动递增ID")
                        # 查找下一个可用ID
                        next_id = new_permission.id
                        while True:
                            next_id += 1
                            check = db.query(UserPermissionFrontend).filter(UserPermissionFrontend.id == next_id).first()
                            if not check:
                                new_permission.id = next_id
                                break
                    
                    # 添加前端权限记录
                    db.add(new_permission)
                    db.flush()  # 提交但不提交事务，以获取生成的ID
                    created_count += 1
                    print(f"创建新前端权限记录: ID={new_permission.id}, 部门={permission.get('department')}, 身份={permission.get('identity')}")
                    
                    # 同步添加后端权限记录
                    # 检查是否已有相同部门和身份的后端权限记录
                    print(f"检查是否存在相同部门({new_permission.department})和身份({new_permission.identity})的后端权限记录")
                    existing_backend = db.query(UserPermissionBackend).filter(
                        UserPermissionBackend.department == new_permission.department,
                        UserPermissionBackend.identity == new_permission.identity
                    ).first()
                    
                    if not existing_backend:
                        # 创建新的后端权限记录
                        backend_max_id_result = db.query(func.max(UserPermissionBackend.id)).first()
                        backend_current_max_id = backend_max_id_result[0] if backend_max_id_result[0] is not None else 0
                        backend_current_max_id += 1
                        
                        new_backend_permission = UserPermissionBackend(
                            id=backend_current_max_id,
                            department=new_permission.department,
                            identity=new_permission.identity,
                            # 所有后端权限默认设为1 (禁止)
                            user_review=1,
                            review_all=1,
                            review_group=1,
                            user_management=1,
                            manage_all=1,
                            manage_group=1,
                            permission_manage=1,
                            form_preset=1,
                            schedule_management=1,
                            distribution_rules=1,
                            distribution_plan=1
                        )
                        
                        # 检查是否已存在相同ID的记录
                        existing = db.query(UserPermissionBackend).filter(UserPermissionBackend.id == new_backend_permission.id).first()
                        if existing:
                            print(f"后端权限表ID {new_backend_permission.id} 已存在，自动递增ID")
                            # 查找下一个可用ID
                            next_id = new_backend_permission.id
                            while True:
                                next_id += 1
                                check = db.query(UserPermissionBackend).filter(UserPermissionBackend.id == next_id).first()
                                if not check:
                                    new_backend_permission.id = next_id
                                    break
                        
                        db.add(new_backend_permission)
                        db.flush()
                        backend_created_count += 1
                        print(f"同步创建后端权限记录: ID={new_backend_permission.id}, 部门={new_permission.department}, 身份={new_permission.identity}")
                    else:
                        print(f"后端权限记录已存在: ID={existing_backend.id}, 部门={existing_backend.department}, 身份={existing_backend.identity}, 跳过创建")
                    
                except Exception as e:
                    db.rollback()
                    print(f"创建新前端记录出错: {str(e)}")
                    import traceback
                    print(traceback.format_exc())
                    raise Exception(f"创建新前端记录出错: {str(e)}")
            else:
                try:
                    # 更新现有记录
                    user_permission = db.query(UserPermissionFrontend).filter(UserPermissionFrontend.id == perm_id).first()
                    
                    if not user_permission:
                        print(f"未找到ID={perm_id}的前端权限记录，跳过")
                        continue
                    
                    # 先获取旧的部门和身份，用于之后更新后端权限
                    old_department = user_permission.department
                    old_identity = user_permission.identity
                    
                    # 更新权限字段
                    for field in permission_fields:
                        if field in permission:
                            new_value = int(permission[field])  # 确保值为整数
                            old_value = getattr(user_permission, field)
                            if old_value != new_value:
                                print(f"更新ID={perm_id}的{field}: {old_value} -> {new_value}")
                                setattr(user_permission, field, new_value)
                    
                    # 更新身份和部门字段
                    new_department = permission.get("department")
                    new_identity = permission.get("identity")
                    
                    if new_department and user_permission.department != new_department:
                        print(f"更新ID={perm_id}的部门: {user_permission.department} -> {new_department}")
                        user_permission.department = new_department
                    
                    if new_identity and user_permission.identity != new_identity:
                        print(f"更新ID={perm_id}的身份: {user_permission.identity} -> {new_identity}")
                        user_permission.identity = new_identity
                    
                    updated_count += 1
                    
                    # 如果部门或身份发生了变化，也更新对应的后端权限记录
                    if new_department != old_department or new_identity != old_identity:
                        # 查找对应的后端权限记录
                        backend_permission = db.query(UserPermissionBackend).filter(
                            UserPermissionBackend.department == old_department,
                            UserPermissionBackend.identity == old_identity
                        ).first()
                        
                        if backend_permission:
                            # 更新后端权限记录的部门和身份
                            backend_permission.department = new_department if new_department else backend_permission.department
                            backend_permission.identity = new_identity if new_identity else backend_permission.identity
                            print(f"同步更新后端权限记录: ID={backend_permission.id}, 部门={backend_permission.department}, 身份={backend_permission.identity}")
                    
                except Exception as e:
                    db.rollback()
                    print(f"更新前端记录出错: {str(e)}")
                    import traceback
                    print(traceback.format_exc())
                    raise Exception(f"更新前端记录出错: {str(e)}")
        
        # 提交事务
        db.commit()
        print(f"成功更新了{updated_count}条前端权限记录，新增了{created_count}条前端权限记录，同步创建了{backend_created_count}条后端权限记录")
        return {
            "status": "success", 
            "updated_count": updated_count,
            "created_count": created_count,
            "backend_created_count": backend_created_count,
            "message": f"成功更新了{updated_count}条前端权限记录，新增了{created_count}条前端权限记录，同步创建了{backend_created_count}条后端权限记录"
        }
    
    except Exception as e:
        db.rollback()
        import traceback
        error_trace = traceback.format_exc()
        print(f"前端权限更新错误: {str(e)}")
        print(f"错误堆栈: {error_trace}")
        raise HTTPException(status_code=500, detail=f"更新前端权限失败: {str(e)}")

# 获取实现用户管理过滤器分组列表的API
@router.get("/api/group-list")
async def get_group_list(request: Request = None):
    # 移除任何可能存在的权限校验
    # 确保即使是管理员也能获取所有分组数据
    try:
        db = SessionLocal()
        groups = db.query(distinct(User.group_name)).filter(
            User.group_name != None, 
            User.group_name != '',
            func.length(User.group_name) > 0
        ).all()
        
        group_list = [group[0] for group in groups]
        print(f"获取到所有分组: {group_list}")  # 添加日志
        return group_list
    except Exception as e:
        print(f"获取分组出错: {str(e)}")  # 添加错误日志
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")
    finally:
        db.close()

# 删除权限记录
@router.delete("/api/delete-permissions/{department}/{identity}")
async def delete_permissions(department: str, identity: str, db: Session = Depends(get_db)):
    try:
        from models import UserPermissionBackend, UserPermissionFrontend
        
        # 查找匹配的后端权限记录
        backend_permission = db.query(UserPermissionBackend).filter(
            UserPermissionBackend.department == department,
            UserPermissionBackend.identity == identity
        ).first()
        
        # 查找匹配的前端权限记录
        frontend_permission = db.query(UserPermissionFrontend).filter(
            UserPermissionFrontend.department == department,
            UserPermissionFrontend.identity == identity
        ).first()
        
        deleted_count = 0
        
        # 删除后端权限记录
        if backend_permission:
            db.delete(backend_permission)
            deleted_count += 1
        
        # 删除前端权限记录
        if frontend_permission:
            db.delete(frontend_permission)
            deleted_count += 1
        
        if deleted_count == 0:
            raise HTTPException(status_code=404, detail=f"未找到部门为'{department}'且身份为'{identity}'的权限记录")
        
        # 提交事务
        db.commit()
        
        return {
            "status": "success",
            "deleted_count": deleted_count,
            "message": f"成功删除部门为'{department}'且身份为'{identity}'的权限记录，共{deleted_count}条"
        }
    
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        import traceback
        error_trace = traceback.format_exc()
        print(f"删除权限记录错误: {str(e)}")
        print(f"错误堆栈: {error_trace}")
        raise HTTPException(status_code=500, detail=f"删除权限记录失败: {str(e)}")

# 获取用户管理权限
@router.get("/api/user-management-permissions")
async def get_user_management_permissions(request: Request, db: Session = Depends(get_db)):
    try:
        # 获取当前登录用户
        current_user = get_current_user(request, db)
        if not current_user:
            raise HTTPException(status_code=401, detail="用户未登录")
        
        # 超级管理员拥有所有权限
        if current_user.is_admin == 0:
            return {
                "manage_all": 0,
                "manage_group": 1,
                "permission_manage": 0
            }
        
        # 如果是普通管理员或普通用户，查询权限表
        from models import UserPermissionBackend
        
        # 根据用户的部门和身份查询权限
        permission = db.query(UserPermissionBackend).filter(
            UserPermissionBackend.department == current_user.department,
            UserPermissionBackend.identity == current_user.identity
        ).first()
        
        if not permission:
            # 没有找到权限记录，返回默认权限（无权限）
            return {
                "manage_all": 1,
                "manage_group": 1,
                "permission_manage": 1
            }
        
        # 返回权限值
        return {
            "manage_all": permission.manage_all,
            "manage_group": permission.manage_group,
            "permission_manage": permission.permission_manage
        }
        
    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        print(f"获取用户管理权限错误: {str(e)}")
        print(f"错误堆栈: {error_trace}")
        raise HTTPException(status_code=500, detail=f"获取用户管理权限失败: {str(e)}")

# 用户角色变更API
@router.post("/api/update-user-role/{user_id}")
async def update_user_role(user_id: str, role: dict):
    try:
        print(f"收到用户角色变更请求: 用户ID={user_id}, 请求数据={role}")
        db = SessionLocal()
        user = db.query(User).filter(User.ID == user_id).first()
        if not user:
            print(f"用户不存在: ID={user_id}")
            raise HTTPException(status_code=404, detail="用户不存在")
        
        # 获取角色值
        is_admin = role.get("is_admin")
        if is_admin is None:
            print(f"请求数据缺少is_admin字段: {role}")
            raise HTTPException(status_code=400, detail="必须选择角色")
        
        # 确保is_admin为整数
        try:
            is_admin = int(is_admin)
            if is_admin not in [0, 1, 2]:
                print(f"无效的is_admin值: {is_admin}")
                raise HTTPException(status_code=400, detail=f"无效的用户角色: {is_admin}")
        except ValueError:
            print(f"is_admin不是有效整数: {is_admin}")
            raise HTTPException(status_code=400, detail=f"用户角色值必须是整数: {is_admin}")
            
        # 不允许将用户变更为超级管理员
        if is_admin == 0:
            print(f"尝试将用户设为超级管理员: ID={user_id}")
            raise HTTPException(status_code=403, detail="不允许将用户设置为超级管理员")
            
        # 检查当前角色是否与目标角色相同
        if user.is_admin == is_admin:
            print(f"用户当前角色与目标角色相同: ID={user_id}, 角色={is_admin}")
            return {"message": "用户角色未更改", "is_admin": is_admin}
        
        # 记录当前角色
        old_role = user.is_admin
            
        # 更新用户角色
        user.is_admin = is_admin
        
        db.commit()
        print(f"用户角色更新成功: ID={user_id}, 角色从{old_role}更改为{is_admin}")
        return {"message": "用户角色更新成功", "is_admin": is_admin}
    except HTTPException as he:
        # 已经格式化的HTTP异常直接抛出
        raise he
    except SQLAlchemyError as e:
        db.rollback()
        print(f"数据库错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"数据库错误: {str(e)}")
    except Exception as e:
        print(f"角色变更时发生未知错误: {str(e)}")
        import traceback
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")
    finally:
        db.close()

# CRM权限管理相关API
@router.post("/api/update-crm-permissions")
async def update_crm_permissions(
    request: CrmPermissionUpdate,
    db: Session = Depends(get_db)
):
    """
    更新CRM权限设置
    
    Args:
        request: 包含权限ID和权限设置的请求体
        db: 数据库会话
        
    Returns:
        更新结果
        
    Request Body:
         {
             "permission_id": "权限记录ID",
             "permissions": {
                 "check_person_crm": 0,     // 查看个人线索 (0=允许, 1=禁止)
                 "check_group_crm": 1,   // 查看组内线索 (0=允许, 1=禁止)
                 "check_all_crm": 1       // 查看全部线索 (0=允许, 1=禁止)
             }
         }
    """
    try:
        # 查找权限记录
        from models import UserPermissionFrontend
        permission_record = db.query(UserPermissionFrontend).filter(
            UserPermissionFrontend.id == request.permission_id
        ).first()
        
        if not permission_record:
            raise HTTPException(status_code=404, detail="权限记录不存在")
        
        # 验证权限字段
        valid_crm_fields = ['check_person_crm', 'check_group_crm', 'check_all_crm']
        for field, value in request.permissions.items():
            if field not in valid_crm_fields:
                raise HTTPException(status_code=400, detail=f"无效的权限字段: {field}")
            if value not in [0, 1]:
                raise HTTPException(status_code=400, detail=f"权限值必须为0或1: {field}={value}")
        
        # 更新权限字段
        for field, value in request.permissions.items():
            if hasattr(permission_record, field):
                setattr(permission_record, field, value)
            else:
                # 如果字段不存在，需要先在数据库表中添加该字段
                raise HTTPException(status_code=400, detail=f"数据库表中不存在字段: {field}")
        
        # 保存更改
        db.commit()
        db.refresh(permission_record)
        
        return {
            "success": True,
            "message": "CRM权限更新成功",
            "permission_id": request.permission_id,
            "updated_permissions": request.permissions
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新CRM权限失败: {str(e)}")

@router.get("/api/crm-permissions/{permission_id}")
async def get_crm_permissions(
    permission_id: str,
    db: Session = Depends(get_db)
):
    """
    获取指定权限记录的CRM权限设置
    
    Args:
        permission_id: 权限记录ID
        db: 数据库会话
        
    Returns:
        CRM权限设置
    """
    try:
        from models import UserPermissionFrontend
        permission_record = db.query(UserPermissionFrontend).filter(
            UserPermissionFrontend.id == permission_id
        ).first()
        
        if not permission_record:
            raise HTTPException(status_code=404, detail="权限记录不存在")
        
        # 提取CRM权限字段
        crm_permissions = {}
        crm_fields = ['check_person_crm', 'check_group_crm', 'check_all_crm']
        
        for field in crm_fields:
            if hasattr(permission_record, field):
                crm_permissions[field] = getattr(permission_record, field)
            else:
                # 如果字段不存在，返回默认值1（禁止）
                crm_permissions[field] = 1
        
        return {
            "success": True,
            "permission_id": permission_id,
            "department": permission_record.department,
            "identity": permission_record.identity,
            "crm_permissions": crm_permissions
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取CRM权限失败: {str(e)}")
